package com.mercaso.data.master_catalog.dto.dashboard;

import jakarta.validation.constraints.NotNull;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@AllArgsConstructor
@Builder
@Getter
@Setter
@ToString
public class DashboardRequest {

    @NotNull
    private UUID storeId;
    @NotNull
    private String upc;
    private Integer daysBefore;
}
