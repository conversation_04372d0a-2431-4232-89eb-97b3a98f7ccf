package com.mercaso.data.third_party.mapper;

import com.mercaso.data.third_party.dto.finale.FinaleFacilityDataResponseDto;
import com.mercaso.data.third_party.dto.finale.FinaleFacilityDto;
import org.mapstruct.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.ArrayList;

@Mapper(componentModel = "spring")
public interface FinaleFacilityMapper {

    @Mapping(target = "contactMechId", source = "contactMech.contactMechId")
    @Mapping(target = "contactMechTypeId", source = "contactMech.contactMechTypeId")
    @Mapping(target = "lastUpdatedDate", ignore = true)
    @Mapping(target = "createdDate", ignore = true)
    FinaleFacilityDto toFinaleFacilityDto(
        String facilityId,
        String facilityUrl,
        String facilityTypeId,
        String statusId,
        String lastUpdatedDate,
        String createdDate,
        String actionUrlDeactivate,
        FinaleFacilityDataResponseDto.ContactMech contactMech,
        String facilityName,
        String parentFacilityUrl,
        Boolean shippingDisabled,
        String actionUrlActivate,
        Boolean receivingDisabled,
        Boolean returnReceivingDisabled
    );

    default List<FinaleFacilityDto> toFinaleFacilityDtoList(FinaleFacilityDataResponseDto responseDto) {
        List<FinaleFacilityDto> result = new ArrayList<>();
        int size = responseDto.getFacilityId().size();
        for (int i = 0; i < size; i++) {
            FinaleFacilityDto dto = toFinaleFacilityDto(
                responseDto.getFacilityId() != null ? responseDto.getFacilityId().get(i) : null,
                responseDto.getFacilityUrl() != null ? responseDto.getFacilityUrl().get(i) : null,
                responseDto.getFacilityTypeId() != null ? responseDto.getFacilityTypeId().get(i) : null,
                responseDto.getStatusId() != null ? responseDto.getStatusId().get(i) : null,
                responseDto.getLastUpdatedDate() != null ? responseDto.getLastUpdatedDate().get(i) : null,
                responseDto.getCreatedDate() != null ? responseDto.getCreatedDate().get(i) : null,
                responseDto.getActionUrlDeactivate() != null ? responseDto.getActionUrlDeactivate().get(i) : null,
                responseDto.getContactMech() != null ? responseDto.getContactMech().get(i) : null,
                responseDto.getFacilityName() != null ? responseDto.getFacilityName().get(i) : null,
                responseDto.getParentFacilityUrl() != null ? responseDto.getParentFacilityUrl().get(i) : null,
                responseDto.getShippingDisabled() != null ? responseDto.getShippingDisabled().get(i) : null,
                responseDto.getActionUrlActivate() != null ? responseDto.getActionUrlActivate().get(i) : null,
                responseDto.getReceivingDisabled() != null ? responseDto.getReceivingDisabled().get(i) : null,
                responseDto.getReturnReceivingDisabled() != null ? responseDto.getReturnReceivingDisabled().get(i) : null
            );
            result.add(dto);
        }
        return result;
    }

    @AfterMapping
    default void setDates(@MappingTarget FinaleFacilityDto target, String lastUpdatedDate, String createdDate) {
        target.setLastUpdatedDate(parseDateTime(lastUpdatedDate));
        target.setCreatedDate(parseDateTime(createdDate));
    }

    default LocalDateTime parseDateTime(String dateTimeString) {
        return LocalDateTime.parse(dateTimeString, DateTimeFormatter.ISO_DATE_TIME);
    }
}