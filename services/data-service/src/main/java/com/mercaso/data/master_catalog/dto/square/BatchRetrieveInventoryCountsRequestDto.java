package com.mercaso.data.master_catalog.dto.square;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Getter
@Setter
public class BatchRetrieveInventoryCountsRequestDto {

    private List<String> locationIds;
    private String updatedAfter;
    private String cursor;
    private List<String> states;
    private Integer limit;
}
