package com.mercaso.data.master_catalog.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.ColumnDefault;

@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@ToString
@Entity
@Table(name = "master_catalog_image")
public class MasterCatalogImage extends BaseEntity {

    @NotNull
    @Column(name = "image_path", nullable = false, length = Integer.MAX_VALUE)
    private String imagePath;

    @NotNull
    @Column(name = "master_catalog_raw_data_id", nullable = false)
    private UUID masterCatalogRawDataId;

    @NotNull
    @ColumnDefault("false")
    @Column(name = "primary_image", nullable = false)
    private Boolean primaryImage = false;
}
