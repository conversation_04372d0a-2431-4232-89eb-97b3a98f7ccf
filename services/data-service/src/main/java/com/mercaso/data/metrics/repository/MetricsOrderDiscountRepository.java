package com.mercaso.data.metrics.repository;

import com.mercaso.data.metrics.entity.MetricsOrderDiscountEntity;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;

public interface MetricsOrderDiscountRepository extends JpaRepository<MetricsOrderDiscountEntity, Long> {

    List<MetricsOrderDiscountEntity> findAllByAddressIdAndDateTypeAndDateLength(String addressId, String timeAggType,
        Integer timeLength);
}
