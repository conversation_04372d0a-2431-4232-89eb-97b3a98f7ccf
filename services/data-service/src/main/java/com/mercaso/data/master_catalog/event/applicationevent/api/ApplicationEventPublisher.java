package com.mercaso.data.master_catalog.event.applicationevent.api;

import com.mercaso.data.master_catalog.event.payload.BusinessEventPayload;

/**
 * Event publisher interface, provides a unified entry point for event publishing
 */
public interface ApplicationEventPublisher {

    /**
     * Publish events using event type enums
     * This method will use the full publishWithStorage logic
     */
    <P extends BusinessEventPayload<?>, E extends Enum<?> & ApplicationEventTypeProvider> void publish(E eventType, P payload);
}

