package com.mercaso.data.third_party.repository.shopify;

import com.mercaso.data.third_party.entity.shopify.order.ShopifyOrdersLineItemEntity;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;

public interface ShopifyOrderLineItemRepository extends JpaRepository<ShopifyOrdersLineItemEntity, Long> {

    List<ShopifyOrdersLineItemEntity> findByOrderIdIn(List<Long> orderIds);

}