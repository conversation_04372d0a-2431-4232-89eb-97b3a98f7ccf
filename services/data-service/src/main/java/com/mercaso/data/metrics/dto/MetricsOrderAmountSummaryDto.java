package com.mercaso.data.metrics.dto;

import com.fasterxml.jackson.databind.JsonNode;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

@Data
public class MetricsOrderAmountSummaryDto {

    private String dateType;
    private LocalDateTime date;
    private BigDecimal totalAmount;
    private Integer totalQuantity;
    private Integer uniqueItems;
    private String department;
    private JsonNode items;
}
