package com.mercaso.data.master_catalog.mapper;

import com.mercaso.data.master_catalog.dto.MasterCatalogSquareInventoryDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogSquareInventory;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants.ComponentModel;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = ComponentModel.SPRING)
public interface MasterCatalogSquareInventoryMapper extends
    BaseDoMapper<MasterCatalogSquareInventoryDto, MasterCatalogSquareInventory> {

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    MasterCatalogSquareInventory partialUpdate(
        MasterCatalogSquareInventoryDto masterCatalogInventoryDto,
        @MappingTarget MasterCatalogSquareInventory masterCatalogInventory);
}