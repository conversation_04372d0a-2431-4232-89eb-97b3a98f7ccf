package com.mercaso.data.metrics.controller;


import com.mercaso.data.metrics.dto.MetricsItemReplenishmentForecastDto;
import com.mercaso.data.metrics.dto.WeeklyItemSalesDto;
import com.mercaso.data.metrics.service.ItemService;
import com.mercaso.data.metrics.service.OrderService;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/sale-statistics")
@RequiredArgsConstructor
@Validated
public class SaleStatisticsController {

    private final OrderService orderService;
    private final ItemService itemService;


    @PreAuthorize("hasAnyAuthority('ds:read:metrics')")
    @GetMapping("/weekly-sku-sales")
    public ResponseEntity<WeeklyItemSalesDto> getWeeklySkuSalesData() {

        WeeklyItemSalesDto weeklyItemSalesDto = orderService.getWeeklySkuSalesData();

        return ResponseEntity.ok(weeklyItemSalesDto);
    }

    @PreAuthorize("hasAnyAuthority('ds:read:metrics')")
    @GetMapping("/item-replenishment-forecast")
    public ResponseEntity<List<MetricsItemReplenishmentForecastDto>> getItemReplenishmentForecast(
        @Valid @NotBlank @RequestParam String addressId, @NotBlank @RequestParam String departmentName) {

        return ResponseEntity.ok(itemService.getItemReplenishmentForecast(addressId, departmentName));
    }

}
