package com.mercaso.data.metrics.repository;


import com.mercaso.data.metrics.entity.MetricsTerritoryDecliningOrderFrequencyEntity;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
public interface MetricsTerritoryDecliningOrderFrequencyRepository extends JpaRepository<MetricsTerritoryDecliningOrderFrequencyEntity,Long> {

    Page<MetricsTerritoryDecliningOrderFrequencyEntity> findByAddressIdIn(List<String> addressIds, Pageable pageable);

    @Query("SELECT DISTINCT addressId FROM MetricsTerritoryDecliningOrderFrequencyEntity")
    List<String> findDistinctAddressIdBy();
}
