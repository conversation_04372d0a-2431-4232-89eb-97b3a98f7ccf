package com.mercaso.data.master_catalog.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.io.Serial;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.ColumnDefault;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@ToString
@Entity
@Table(name = "master_catalog_product")
public class MasterCatalogProduct extends BaseEntity {

    @Serial
    private static final long serialVersionUID = -7659005238168175849L;

    @Size(max = 255)
    @Column(name = "upc")
    private String upc;

    @Size(max = 255)
    @NotNull
    @Column(name = "name", nullable = false)
    private String name;

    @NotNull
    @Column(name = "description", nullable = false, length = Integer.MAX_VALUE)
    private String description;

    @Size(max = 255)
    @Column(name = "brand")
    private String brand;

    @Size(max = 255)
    @Column(name = "sku_number")
    private String skuNumber;

    @Size(max = 255)
    @Column(name = "department")
    private String department;

    @Size(max = 255)
    @Column(name = "category")
    private String category;

    @Size(max = 255)
    @Column(name = "sub_category")
    private String subCategory;

    @Size(max = 255)
    @Column(name = "clazz")
    private String clazz;

    @Size(max = 255)
    @Column(name = "primary_vendor")
    private String primaryVendor;

    @NotNull
    @Column(name = "master_catalog_raw_data_id", nullable = false)
    private UUID masterCatalogRawDataId;

    @ColumnDefault("false")
    @Column(name = "single_product")
    private Boolean singleProduct;

}
