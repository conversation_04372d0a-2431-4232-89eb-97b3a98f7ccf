package com.mercaso.data.master_catalog.mapper;

import com.mercaso.data.master_catalog.dto.square.BatchRetrieveInventoryCountsRequestDto;
import com.squareup.square.models.BatchRetrieveInventoryCountsRequest;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants.ComponentModel;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = ComponentModel.SPRING)
public interface SquareBatchRetrieveInventoryCountsRequestMapper {

    BatchRetrieveInventoryCountsRequest toExternalDto(BatchRetrieveInventoryCountsRequestDto dto);

    BatchRetrieveInventoryCountsRequestDto fromExternalDto(BatchRetrieveInventoryCountsRequest entity);

}
