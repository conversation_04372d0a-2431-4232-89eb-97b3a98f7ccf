package com.mercaso.data.metrics.controller;

import com.mercaso.data.dto.CustomPage;
import com.mercaso.data.metrics.dto.MetricsTerritoryAlertAggregatedDto;
import com.mercaso.data.metrics.dto.MetricsTerritoryAlertDto;
import com.mercaso.data.metrics.dto.MetricsTerritorySalespersonDto;
import com.mercaso.data.metrics.enums.AlertType;
import com.mercaso.data.metrics.service.MetricsTerritoryService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/metrics/territory")
@RequiredArgsConstructor
public class MetricsTerritoryController {

    private final MetricsTerritoryService metricsTerritoryService;

    @PreAuthorize("hasAnyAuthority('ds:read:metrics')")
    @GetMapping("/salesperson")
    public ResponseEntity<CustomPage<MetricsTerritorySalespersonDto>> getSalespersons(){

        Page<MetricsTerritorySalespersonDto> salespersonsDtoPage = metricsTerritoryService.getSalespersons();
        CustomPage<MetricsTerritorySalespersonDto> result = new CustomPage<MetricsTerritorySalespersonDto>().build(salespersonsDtoPage);
        return ResponseEntity.ok(result);
    }


    @PreAuthorize("hasAnyAuthority('ds:read:metrics')")
    @GetMapping("/alert")
    public ResponseEntity<CustomPage<MetricsTerritoryAlertDto>> getAlert(
        @RequestParam AlertType alertType,
        @RequestParam List<String> zipCodes) {

        Page<MetricsTerritoryAlertDto> alertDtoPage = metricsTerritoryService.getAlert(alertType, zipCodes);
        CustomPage<MetricsTerritoryAlertDto> result = new CustomPage<MetricsTerritoryAlertDto>().build(alertDtoPage);
        return ResponseEntity.ok(result);
    }

    @PreAuthorize("hasAnyAuthority('ds:read:metrics')")
    @GetMapping("/aggregate-alert")
    public ResponseEntity<MetricsTerritoryAlertAggregatedDto> getAggregateAlert(@RequestParam List<String> zipCodes) {

        MetricsTerritoryAlertAggregatedDto aggregatedAlert = metricsTerritoryService.getAggregatedAlert(zipCodes);
        return ResponseEntity.ok(aggregatedAlert);
    }

}
