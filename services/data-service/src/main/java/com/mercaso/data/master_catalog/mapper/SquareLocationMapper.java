package com.mercaso.data.master_catalog.mapper;

import static com.mercaso.data.utils.SerializationUtils.readTree;

import com.fasterxml.jackson.databind.JsonNode;
import com.mercaso.data.master_catalog.dto.MasterCatalogLocationDto;
import com.squareup.square.models.Location;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants.ComponentModel;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = ComponentModel.SPRING)
public interface SquareLocationMapper {


    @Mapping(target = "addressLine1", source = "address.addressLine1")
    @Mapping(target = "postalCode", source = "address.postalCode")
    @Mapping(target = "city", source = "address.locality")
    @Mapping(target = "country", source = "address.country")
    @Mapping(target = "latitude", expression = "java(location.getCoordinates() != null ? location.getCoordinates().getLatitude() : null)")
    @Mapping(target = "longitude", expression = "java(location.getCoordinates() != null ? location.getCoordinates().getLongitude() : null)")
    @Mapping(target = "metadata", expression = "java(createMetadata(location))")
    MasterCatalogLocationDto from(Location location);

    default JsonNode createMetadata(Location location) {
        try {
            return readTree("{\"merchantId\":\"" + location.getMerchantId() + "\", \"locationId\":\"" + location.getId() + "\"}");
        } catch (Exception e) {
            throw new RuntimeException("Failed to create metadata JsonNode", e);
        }
    }
}
