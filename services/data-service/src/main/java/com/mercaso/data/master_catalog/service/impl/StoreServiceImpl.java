package com.mercaso.data.master_catalog.service.impl;

import com.mercaso.data.master_catalog.dto.StoreDto;
import com.mercaso.data.master_catalog.mapper.StoreMapper;
import com.mercaso.data.master_catalog.repository.StoreRepository;
import com.mercaso.data.master_catalog.service.StoreService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
public class StoreServiceImpl implements StoreService {


    private final StoreRepository storeRepository;


    private final StoreMapper storeMapper;

    @Override
    public List<StoreDto> getStores() {
        return storeRepository.findAll().stream().map(storeMapper::toDto).toList();
    }

}
