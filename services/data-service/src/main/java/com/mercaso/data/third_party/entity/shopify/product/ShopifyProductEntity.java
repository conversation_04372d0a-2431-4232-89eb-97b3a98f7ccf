package com.mercaso.data.third_party.entity.shopify.product;

import com.fasterxml.jackson.databind.JsonNode;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.time.ZonedDateTime;
import java.util.List;
import lombok.Data;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

@Entity
@Table(schema = "shopify", name = "products")
@Data
public class ShopifyProductEntity {

    @Id
    private Long id;

    @Column(columnDefinition = "text")
    private String bodyHtml;

    private ZonedDateTime createdAt;
    private String handle;
    private String productType;
    private ZonedDateTime publishedAt;
    private String publishedScope;
    private String status;
    private String tags;
    private String templateSuffix;
    private String title;
    private ZonedDateTime updatedAt;
    private String vendor;
    private String adminGraphqlApiId;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    private JsonNode image;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    private List<ShopifyProductImageEntity> images;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    private List<ShopifyProductOptionEntity> options;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    private List<ShopifyProductVariantEntity> variants;
}