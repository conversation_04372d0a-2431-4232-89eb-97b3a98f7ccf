package com.mercaso.data.master_catalog.mapper;

import com.mercaso.data.master_catalog.dto.MasterCatalogRawDataDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawData;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants.ComponentModel;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = ComponentModel.SPRING)
public interface MasterCatalogRawDataMapper extends BaseDoMapper<MasterCatalogRawDataDto, MasterCatalogRawData> {

    MasterCatalogRawData toEntity(MasterCatalogRawDataDto masterCatalogRawDataDto);

    MasterCatalogRawDataDto toDto(MasterCatalogRawData masterCatalogRawData);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    MasterCatalogRawData partialUpdate(
        MasterCatalogRawDataDto masterCatalogRawDataDto, @MappingTarget MasterCatalogRawData masterCatalogRawData);
}