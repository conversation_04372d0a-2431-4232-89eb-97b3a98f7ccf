package com.mercaso.ims.utils.finale;

import com.mercaso.ims.infrastructure.external.finale.dto.QueryFinalePurchaseOrderResultDto.PartySupplierGroupName;
import com.mercaso.ims.infrastructure.external.finale.dto.QueryFinalePurchaseOrderResultDto.PurchaseOrderPageInfo;
import org.apache.commons.lang3.RandomStringUtils;

public class PurchaseOrderPageInfoUtil {

    public static PurchaseOrderPageInfo buildPurchaseOrderPageInfo() {
        PartySupplierGroupName partySupplierGroupName = new PartySupplierGroupName();
        partySupplierGroupName.setName("Vendor Name");
        partySupplierGroupName.setPartyId(RandomStringUtils.randomAlphabetic(6));

        PurchaseOrderPageInfo purchaseOrderPageInfo = new PurchaseOrderPageInfo();
        purchaseOrderPageInfo.setOrderId(RandomStringUtils.randomAlphabetic(6));
        purchaseOrderPageInfo.setOrderDateFormatted("2024-03-12T10:00:00Z");
        purchaseOrderPageInfo.setShipmentsFormatted("Received 4/15/2025");
        purchaseOrderPageInfo.setReceiveDateFormatted("4/15/2025");
        purchaseOrderPageInfo.setStatusIdFormatted("Completed");
        purchaseOrderPageInfo.setPartySupplierGroupName(partySupplierGroupName);
        return purchaseOrderPageInfo;


    }

}
