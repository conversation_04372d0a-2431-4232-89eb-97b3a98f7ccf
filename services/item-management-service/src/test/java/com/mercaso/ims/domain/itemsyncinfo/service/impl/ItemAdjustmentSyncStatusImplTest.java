package com.mercaso.ims.domain.itemsyncinfo.service.impl;

import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.atLeast;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.domain.itemsyncinfo.ItemAdjustmentSyncStatus;
import com.mercaso.ims.domain.itemsyncinfo.ItemAdjustmentSyncStatusRepository;
import com.mercaso.ims.domain.itemsyncinfo.enums.SyncShopifyStatus;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.aot.DisabledInAotMode;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ContextConfiguration(classes = {ItemAdjustmentSyncStatusServiceImpl.class})
@ExtendWith(SpringExtension.class)
@DisabledInAotMode
class ItemAdjustmentSyncStatusImplTest {

    @MockBean
    private ItemAdjustmentSyncStatusRepository itemSyncInfoRepository;

    @Autowired
    private ItemAdjustmentSyncStatusServiceImpl itemAdjustmentSyncStatusServiceImpl;

    @Test
    void testFindByBusinessEventId() {
        // Arrange
        when(itemSyncInfoRepository.findByBusinessEventId(Mockito.<UUID>any())).thenReturn(null);

        // Act
        ItemAdjustmentSyncStatus actualFindByBusinessEventIdResult = itemAdjustmentSyncStatusServiceImpl.findByBusinessEventId(
            UUID.randomUUID());

        // Assert
        verify(itemSyncInfoRepository).findByBusinessEventId(isA(UUID.class));
        assertNull(actualFindByBusinessEventIdResult);
    }

    @Test
    void testSave() {
        // Arrange
        when(itemSyncInfoRepository.save(Mockito.<ItemAdjustmentSyncStatus>any())).thenReturn(null);

        // Act
        ItemAdjustmentSyncStatus actualSaveResult = itemAdjustmentSyncStatusServiceImpl.save(null);

        // Assert
        verify(itemSyncInfoRepository).save(isNull());
        assertNull(actualSaveResult);
    }

    @Test
    void testUpdate() {
        // Arrange
        when(itemSyncInfoRepository.update(Mockito.<ItemAdjustmentSyncStatus>any())).thenReturn(null);

        // Act
        ItemAdjustmentSyncStatus actualUpdateResult = itemAdjustmentSyncStatusServiceImpl.update(null);

        // Assert
        verify(itemSyncInfoRepository).update(isNull());
        assertNull(actualUpdateResult);
    }

    @Test
    void testSaveItemAdjustmentSyncFiledStatus() {
        // Arrange
        when(itemSyncInfoRepository.findByBusinessEventId(Mockito.<UUID>any())).thenReturn(null);
        when(itemSyncInfoRepository.save(Mockito.<ItemAdjustmentSyncStatus>any())).thenReturn(null);

        // Act
        itemAdjustmentSyncStatusServiceImpl.saveItemAdjustmentSyncFiledStatus(UUID.randomUUID());

        // Assert
        verify(itemSyncInfoRepository).findByBusinessEventId(isA(UUID.class));
        verify(itemSyncInfoRepository).save(isA(ItemAdjustmentSyncStatus.class));
    }


    @Test
    void testSaveItemAdjustmentSyncFiledStatus2() {
        // Arrange
        ItemAdjustmentSyncStatus itemAdjustmentSyncStatus = mock(ItemAdjustmentSyncStatus.class);
        when(itemAdjustmentSyncStatus.getSyncShopifyStatus()).thenReturn(SyncShopifyStatus.SUCCESS);
        doNothing().when(itemAdjustmentSyncStatus).syncShopifyFailure();
        when(itemSyncInfoRepository.findByBusinessEventId(Mockito.<UUID>any()))
            .thenReturn(itemAdjustmentSyncStatus);
        when(itemSyncInfoRepository.save(Mockito.<ItemAdjustmentSyncStatus>any())).thenReturn(null);

        // Act
        itemAdjustmentSyncStatusServiceImpl.saveItemAdjustmentSyncFiledStatus(UUID.randomUUID());

        // Assert that nothing has changed
        verify(itemAdjustmentSyncStatus, atLeast(1)).getSyncShopifyStatus();
        verify(itemAdjustmentSyncStatus).syncShopifyFailure();
        verify(itemSyncInfoRepository).findByBusinessEventId(isA(UUID.class));
        verify(itemSyncInfoRepository).save(isA(ItemAdjustmentSyncStatus.class));
    }


    @Test
    void testSaveItemAdjustmentSyncFiledStatus3() {
        // Arrange
        ItemAdjustmentSyncStatus itemAdjustmentSyncStatus = mock(ItemAdjustmentSyncStatus.class);
        when(itemAdjustmentSyncStatus.getSyncShopifyStatus()).thenReturn(SyncShopifyStatus.FAILURE);
        when(itemSyncInfoRepository.findByBusinessEventId(Mockito.<UUID>any()))
            .thenReturn(itemAdjustmentSyncStatus);

        // Act
        itemAdjustmentSyncStatusServiceImpl.saveItemAdjustmentSyncFiledStatus(UUID.randomUUID());

        // Assert that nothing has changed
        verify(itemAdjustmentSyncStatus, atLeast(1)).getSyncShopifyStatus();
        verify(itemSyncInfoRepository).findByBusinessEventId(isA(UUID.class));
    }
}
