package com.mercaso.ims.application.dto.payload;

import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.infrastructure.event.BusinessEventPayloadDto;
import lombok.*;

import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ItemCreatedPayloadDto extends BusinessEventPayloadDto<ItemDto> {

    private UUID itemId;

    private UUID itemAdjustmentRequestDetailId;

    @Builder
    public ItemCreatedPayloadDto(ItemDto data, UUID itemId, UUID itemAdjustmentRequestDetailId) {
        super(data);
        this.itemId = itemId;
        this.itemAdjustmentRequestDetailId = itemAdjustmentRequestDetailId;
    }
}