package com.mercaso.ims.application.command;

import com.mercaso.ims.application.BaseCommand;
import java.math.BigDecimal;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;


@Data
@Builder
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
public class UpdateItemTargetCostChangeRequestCommand extends BaseCommand {

    private UUID id;

    private BigDecimal previousCost;

    private BigDecimal targetCost;

}