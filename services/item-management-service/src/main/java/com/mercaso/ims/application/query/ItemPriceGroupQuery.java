package com.mercaso.ims.application.query;

import com.mercaso.ims.infrastructure.repository.PageQuery;
import java.time.Instant;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@SuperBuilder
public class ItemPriceGroupQuery extends PageQuery {

    private Instant createdAtBegin;

    private Instant createdAtEnd;


    private String createdBy;

    private String groupName;

    private SortType sort;

    @Getter
    public enum SortType {
        CREATED_AT_DESC,
        CREATED_AT_ASC,
        GROUP_NAME_DESC,
        GROUP_NAME_ASC,
        PRICE_DESC,
        PRICE_ASC,
    }
}
