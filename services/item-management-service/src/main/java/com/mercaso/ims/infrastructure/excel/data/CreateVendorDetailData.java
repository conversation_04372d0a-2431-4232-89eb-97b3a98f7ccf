package com.mercaso.ims.infrastructure.excel.data;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode
public class CreateVendorDetailData {

    @ExcelProperty("requestStatus")
    private String status;
    @ExcelProperty("failureReason")
    private String failureReason;
    @ExcelProperty("Vendor Name")
    @ColumnWidth(20)
    private String vendor;
}
