package com.mercaso.ims.domain.category;

import com.mercaso.ims.application.command.CreateCategoryCommand;

import com.mercaso.ims.domain.category.enums.CategoryStatus;
import java.time.Instant;

public class CategoryFactory {
    public static Category create(CreateCategoryCommand command) {
        return Category.builder()
                .name(command.getCategoryName())
                .status(null == command.getCategoryStatus() ? CategoryStatus.ACTIVE : command.getCategoryStatus())
                .icon(command.getIcon())
                .description(command.getDescription())
                .createdAt(Instant.now())
                .build();
    }

    private CategoryFactory() {
    }
}
