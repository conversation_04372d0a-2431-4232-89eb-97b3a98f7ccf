package com.mercaso.ims.domain.itemcostcollection;

import com.mercaso.ims.application.command.CreateItemCostCollectionCommand;

public class ItemCostCollectionFactory {

    private ItemCostCollectionFactory() {
    }

    public static ItemCostCollection createItemCostCollection(CreateItemCostCollectionCommand command) {
        return ItemCostCollection.builder()
            .vendorId(command.getVendorId())
            .vendorName(command.getVendorName())
            .vendorCollectionNumber(command.getVendorCollectionNumber())
            .fileName(command.getFileName())
            .source(command.getSource())
            .type(command.getType())
            .build();
    }


}
