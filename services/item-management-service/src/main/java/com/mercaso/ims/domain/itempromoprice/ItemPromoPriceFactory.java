package com.mercaso.ims.domain.itempromoprice;

import com.mercaso.ims.application.command.CreateItemPromoPriceCommand;
import com.mercaso.ims.application.dto.PriceDto;
import com.mercaso.ims.domain.itempromoprice.enums.ItemPromoPriceStatus;
import java.math.BigDecimal;

public class ItemPromoPriceFactory {

    private ItemPromoPriceFactory() {
        throw new IllegalStateException("Factory class");
    }

    public static ItemPromoPrice create(CreateItemPromoPriceCommand command, Integer packSize, Boolean crvFlag, BigDecimal crv) {

        ItemPromoPrice itemPromoPrice = ItemPromoPrice.builder()
            .itemId(command.getItemId())
            .itemPromoPriceStatus(ItemPromoPriceStatus.DRAFT)
            .promoFlag(command.getPromoFlag())
            .promoBeginTime(command.getPromoBeginTime())
            .promoEndTime(command.getPromoEndTime())
            .crv(crv)
            .crvFlag(crvFlag)
            .build();

        return itemPromoPrice.updatePrice(new PriceDto(command.getPromoPrice(),
            packSize,
            crvFlag,
            crv));
    }
}
