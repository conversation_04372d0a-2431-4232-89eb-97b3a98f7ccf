package com.mercaso.ims.application.service.impl;

import com.mercaso.ims.application.command.CreateTestOrderCommand;
import com.mercaso.ims.application.dto.TestOrderDto;
import com.mercaso.ims.application.dto.payload.TestOrderCreatedPayloadDto;
import com.mercaso.ims.application.mapper.testorder.TestOrderDtoApplicationMapper;
import com.mercaso.ims.application.service.TestOrderApplicationService;
import com.mercaso.ims.domain.businessevent.service.BusinessEventService;
import com.mercaso.ims.domain.testorder.TestOrder;
import com.mercaso.ims.domain.testorder.TestOrderFactory;
import com.mercaso.ims.domain.testorder.TestOrderRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@RequiredArgsConstructor
@Service
@Slf4j
public class TestOrderOrderApplicationServiceImpl implements TestOrderApplicationService {


    private final BusinessEventService businessEventService;
    private final TestOrderRepository testOrderRepository;
    private final TestOrderDtoApplicationMapper testOrderDtoApplicationMapper;


    @Override
    @Transactional
    public TestOrderDto create(CreateTestOrderCommand createTestOrderCommand) {
        TestOrder testOrder = TestOrderFactory.create(createTestOrderCommand);
        testOrder = testOrderRepository.save(testOrder);
        TestOrderDto testOrderDto = testOrderDtoApplicationMapper.domainToDto(testOrder);
        businessEventService.dispatch(TestOrderCreatedPayloadDto.builder()
                .testOrderId(testOrderDto.getId())
                .data(testOrderDto)
                .build());
        return testOrderDto;
    }
}
