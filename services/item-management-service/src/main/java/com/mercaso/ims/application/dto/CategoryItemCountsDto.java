package com.mercaso.ims.application.dto;

import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CategoryItemCountsDto extends BaseDto {
    private UUID categoryId;

    private Long activeCount;

    private Long draftCount;

    private Long archivedCount;
}
