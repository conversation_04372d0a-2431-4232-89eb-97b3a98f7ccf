package com.mercaso.ims.domain.brand.service.impl;

import com.mercaso.ims.domain.brand.Brand;
import com.mercaso.ims.domain.brand.BrandRepository;
import com.mercaso.ims.domain.brand.service.BrandService;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class BrandServiceImpl implements BrandService {

    private final BrandRepository brandRepository;

    @Override
    public Brand findById(UUID id) {
        return brandRepository.findById(id);
    }

    @Override
    public Brand findByName(String name) {
        return brandRepository.findByName(name);
    }

    @Override
    public Brand save(Brand brand) {
        return brandRepository.save(brand);
    }

    @Override
    public Brand update(Brand brand) {
        return brandRepository.update(brand);
    }

    @Override
    public void delete(UUID id) {
        brandRepository.deleteById(id);
    }


    @Override
    public List<Brand> findByIds(List<UUID> ids) {
        return brandRepository.findByIds(ids);
    }

    @Override
    public List<Brand> findAll() {
        return brandRepository.findAll();
    }

    @Override
    public List<Brand> findByFuzzyName(String name) {
        return brandRepository.findByFuzzyName(name);
    }

    @Override
    public List<Brand> findByNameIgnoreCase(String name) {
        return brandRepository.findByNameIgnoreCase(name);
    }
}
