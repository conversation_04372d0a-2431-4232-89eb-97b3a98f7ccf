package com.mercaso.ims.interfaces.rest.query;

import com.mercaso.ims.application.dto.ItemCostCollectionDetailDto;
import com.mercaso.ims.application.queryservice.ItemCostCollectionQueryApplicationService;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping(value = "/v1/query/item-cost-collection", produces = {MediaType.APPLICATION_JSON_VALUE})
@RequiredArgsConstructor
public class QueryItemCostCollectionRestApi {

    private final ItemCostCollectionQueryApplicationService itemCostCollectionQueryApplicationService;

    @GetMapping("/{itemCostCollectionId}")
    @PreAuthorize("hasAuthority('ims:read:vendor-items')")
    public ItemCostCollectionDetailDto findByItemCostCollectionId(@PathVariable UUID itemCostCollectionId) {
        log.info("[findByItemCostCollectionId] param itemCostCollectionId: {}.", itemCostCollectionId);
        return itemCostCollectionQueryApplicationService.findByItemCostCollectionId(itemCostCollectionId);
    }
}