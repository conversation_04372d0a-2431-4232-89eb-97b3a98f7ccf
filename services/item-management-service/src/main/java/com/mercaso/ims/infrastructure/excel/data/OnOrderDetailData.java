package com.mercaso.ims.infrastructure.excel.data;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class OnOrderDetailData {

    @ExcelProperty("PO #")
    private String poNumber;

    @ExcelProperty("DATE")
    @ColumnWidth(12)
    private String pickupDate;

    @ExcelProperty("Mercaso SKU")
    @ColumnWidth(15)
    private String sku;
    @ExcelProperty("Description")
    @ColumnWidth(30)
    private String description;

    @ExcelProperty("Cases")
    private Integer quantity;

}
