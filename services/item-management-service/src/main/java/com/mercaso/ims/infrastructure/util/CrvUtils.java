package com.mercaso.ims.infrastructure.util;

import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.INVALID_BOTTLE_UNIT;

import com.alibaba.excel.util.StringUtils;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import java.math.BigDecimal;

public class CrvUtils {

    private CrvUtils() {
    }

    //2021-03-24T00:00:00Z
    public static BigDecimal getCrv(Float bottleSize, String bottleUnit) {
        if (bottleSize == null || StringUtils.isEmpty(bottleUnit)) {
            return BigDecimal.ZERO;
        }
        if (!VolumeUtil.isValidBottleUnit(bottleUnit)) {
            throw new ImsBusinessException(INVALID_BOTTLE_UNIT);
        }
        double bottle = VolumeUtil.convertToOunces(bottleSize, bottleUnit);
        if (bottle > 0) {
            if (bottle >= 24) {
                return BigDecimal.valueOf(0.1);
            } else {
                return BigDecimal.valueOf(0.05);
            }
        }
        return BigDecimal.ZERO;
    }


}
