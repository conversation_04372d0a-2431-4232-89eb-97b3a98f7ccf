package com.mercaso.ims.domain.attribute;

import com.mercaso.ims.domain.BaseDomain;
import com.mercaso.ims.domain.attribute.enums.AttributeFormat;
import com.mercaso.ims.domain.attribute.enums.AttributeStatus;
import com.mercaso.ims.domain.attributeenumvalue.AttributeEnumValue;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.springframework.beans.factory.annotation.Configurable;

import java.util.List;
import java.util.UUID;

@Data
@ToString
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@Configurable(preConstruction = true)
public class Attribute extends BaseDomain {

    private final UUID id;

    private String name;

    private UUID categoryId;

    private String description;

    private AttributeFormat attributeFormat;

    private AttributeStatus status;

    private List<AttributeEnumValue> attributeEnumValues;


    public Attribute updateAttributeInfo(String name, String description, AttributeFormat attributeFormat) {
        this.name = name;
        this.description = description;
        this.attributeFormat = attributeFormat;
        return this;
    }

    public Attribute addAttributeEnumValue(AttributeEnumValue attributeEnumValue) {
        this.attributeEnumValues.add(attributeEnumValue);
        return this;
    }

    public Attribute removeAttributeEnumValue(UUID attributeEnumValueId) {
        this.attributeEnumValues.removeIf(attributeEnumValue -> attributeEnumValue.getId().equals(attributeEnumValueId));
        return this;
    }
}
