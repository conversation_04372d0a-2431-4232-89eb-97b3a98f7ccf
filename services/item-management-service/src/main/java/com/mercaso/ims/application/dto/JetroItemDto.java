package com.mercaso.ims.application.dto;

import java.math.BigDecimal;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class JetroItemDto extends BaseDto {

    private UUID itemId;

    private String productName;
    
    private String currency;

    private BigDecimal price;

    private Boolean availability;

    private String rating;
}
