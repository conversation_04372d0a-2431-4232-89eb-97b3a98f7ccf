package com.mercaso.ims.domain.itemsyncinfo;
import com.mercaso.ims.domain.BaseDomainRepository;
import com.mercaso.ims.domain.itemsyncinfo.enums.SyncShopifyStatus;
import java.util.List;
import java.util.UUID;

public interface ItemAdjustmentSyncStatusRepository extends BaseDomainRepository<ItemAdjustmentSyncStatus, UUID> {

    ItemAdjustmentSyncStatus findByBusinessEventId(UUID businessEventId);

    ItemAdjustmentSyncStatus save(ItemAdjustmentSyncStatus itemSyncInfo);

    ItemAdjustmentSyncStatus update(ItemAdjustmentSyncStatus itemSyncInfo);

    List<ItemAdjustmentSyncStatus> findBySyncShopifyStatus(SyncShopifyStatus syncShopifyStatus);
}
