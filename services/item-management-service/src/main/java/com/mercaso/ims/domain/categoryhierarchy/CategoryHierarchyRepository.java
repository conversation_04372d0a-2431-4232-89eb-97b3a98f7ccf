package com.mercaso.ims.domain.categoryhierarchy;

import com.mercaso.ims.domain.BaseDomainRepository;
import java.util.List;
import java.util.UUID;

public interface CategoryHierarchyRepository extends BaseDomainRepository<CategoryHierarchy, UUID> {

    List<CategoryHierarchy> findByCategoryId(UUID categoryId);

    List<CategoryHierarchy> findByCategoryIdIn(List<UUID> categoryIds);

    List<CategoryHierarchy> findByAncestorCategoryId(UUID ancestorCategoryId);

    List<CategoryHierarchy> findByDepth(Integer depth);

    CategoryHierarchy findByCategoryIdAndAncestorCategoryId (UUID categoryId, UUID ancestorCategoryId);

    List<CategoryHierarchy> findByAncestorCategoryIdAndDepth (UUID ancestorCategoryId, Integer depth);

    List<CategoryHierarchy> findByCategoryIdAndDepth (UUID ancestorCategoryId, Integer depth);



}
