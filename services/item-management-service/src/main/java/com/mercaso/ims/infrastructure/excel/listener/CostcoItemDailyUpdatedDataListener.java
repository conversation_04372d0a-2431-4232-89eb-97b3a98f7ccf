package com.mercaso.ims.infrastructure.excel.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.mercaso.ims.infrastructure.excel.data.CostcoItemDailyUpdatedData;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CostcoItemDailyUpdatedDataListener implements ReadListener<CostcoItemDailyUpdatedData> {

    private List<CostcoItemDailyUpdatedData> costcoItemDailyUpdatedDataList;


    public CostcoItemDailyUpdatedDataListener(List<CostcoItemDailyUpdatedData> costcoItemDailyUpdatedDataList) {

        this.costcoItemDailyUpdatedDataList = costcoItemDailyUpdatedDataList;
    }

    @Override
    public void invoke(CostcoItemDailyUpdatedData data, AnalysisContext analysisContext) {
        try {
            costcoItemDailyUpdatedDataList.add(data);
        } catch (Exception e) {
            log.error("CreateVendorRequestDataListener error", e);
        }

    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.info("All data is read successfully");
    }


}