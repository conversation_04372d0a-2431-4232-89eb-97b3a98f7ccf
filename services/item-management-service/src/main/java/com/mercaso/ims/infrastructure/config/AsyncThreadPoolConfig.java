package com.mercaso.ims.infrastructure.config;


import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

@EnableAsync
@Configuration
public class AsyncThreadPoolConfig implements AsyncConfigurer {

    @Value("${spring.async.core_pool_size}")
    private Integer corePoolSize;
    @Value("${spring.async.max_pool_size}")
    private Integer maxPoolSize;
    @Value("${spring.async.queue_capacity}")
    private Integer queueCapacity;
    @Value("${spring.async.alive_time}")
    private Integer aliveTime;

    @Value("${shopify_sync.core_pool_size}")
    private Integer shopifySyncCorePoolSize;
    @Value("${shopify_sync.max_pool_size}")
    private Integer shopifySyncMaxPoolSize;
    @Value("${shopify_sync.queue_capacity}")
    private Integer shopifySyncQueueCapacity;
    @Value("${shopify_sync.alive_time}")
    private Integer shopifySyncAliveTime;

    @Bean("taskExecutor")
    public Executor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setQueueCapacity(queueCapacity);
        executor.setKeepAliveSeconds(shopifySyncAliveTime);
        executor.setThreadNamePrefix("AsyncTaskExecutor-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setTaskDecorator(new ContextCopyingDecorator());
        executor.initialize();
        return executor;
    }

    @Bean("syncShopifyTaskExecutor")
    public Executor syncShopifyTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(shopifySyncCorePoolSize);
        executor.setMaxPoolSize(shopifySyncMaxPoolSize);
        executor.setQueueCapacity(shopifySyncQueueCapacity);
        executor.setKeepAliveSeconds(shopifySyncAliveTime);
        executor.setThreadNamePrefix("ShopifyAsyncTaskExecutor-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setTaskDecorator(new ContextCopyingDecorator());
        executor.initialize();
        return executor;
    }

}
