package com.mercaso.ims.application.dto;

import com.mercaso.ims.domain.itemcostchangerequest.enums.ItemCostChangeRequestStatus;
import com.mercaso.ims.domain.itemcostchangerequest.enums.MatchedType;
import java.math.BigDecimal;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ItemCostChangeRequestDto extends BaseDto {

    private UUID id;

    private UUID itemCostCollectionId;

    private UUID vendorId;

    private UUID itemId;

    private String skuNumber;

    private String itemStatus;

    private String vendorSkuNumber;

    private String vendorItemName;

    private BigDecimal regPrice;

    private BigDecimal previousCost;

    private BigDecimal targetCost;

    private BigDecimal tax;

    private BigDecimal crv;

    private MatchedType matchType;

    private ItemCostChangeRequestStatus status;

    private UUID primaryVendorId;

    private Boolean isPrimaryVendor;

    private UUID backupVendorId;

    private Boolean isBackupVendor;

    private String vendorItemUpc;

    private Boolean availability;
    private String costType;
    private String aisle;
}
