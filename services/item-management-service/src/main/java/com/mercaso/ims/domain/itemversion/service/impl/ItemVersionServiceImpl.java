package com.mercaso.ims.domain.itemversion.service.impl;

import com.mercaso.ims.domain.itemversion.ItemVersion;
import com.mercaso.ims.domain.itemversion.ItemVersionRepository;
import com.mercaso.ims.domain.itemversion.service.ItemVersionService;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ItemVersionServiceImpl implements ItemVersionService {

    private static final Logger log = LoggerFactory.getLogger(ItemVersionServiceImpl.class);
    private final ItemVersionRepository itemVersionRepository;


    @Override
    public ItemVersion findBySkuAndVersion(String skuNumber, Integer version) {
        return itemVersionRepository.findBySkuAndVersion(skuNumber, version);
    }

    @Override
    public ItemVersion findByItemIdAndVersion(UUID itemId, Integer version) {
        return itemVersionRepository.findByItemIdAndVersion(itemId, version);
    }

    @Override
    public List<ItemVersion> findByItemId(UUID itemId) {
        return itemVersionRepository.findByItemId(itemId);
    }

    @Override
    public ItemVersion save(ItemVersion itemVersion) {
        return itemVersionRepository.save(itemVersion);
    }

    @Override
    public ItemVersion update(ItemVersion itemVersion) {
        return itemVersionRepository.update(itemVersion);
    }

    @Override
    public ItemVersion delete(UUID id) {
        return itemVersionRepository.deleteById(id);
    }
}
