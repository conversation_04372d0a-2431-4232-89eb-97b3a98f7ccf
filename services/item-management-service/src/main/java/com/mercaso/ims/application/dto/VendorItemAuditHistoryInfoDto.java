package com.mercaso.ims.application.dto;

import com.mercaso.ims.domain.businessevent.enums.EventTypeEnums;
import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class VendorItemAuditHistoryInfoDto extends BaseDto {

    private String updatedBy;

    private String updatedUserName;

    private Instant updatedAt;

    private EventTypeEnums type;

    private VendorItemDto previous;

    private VendorItemDto current;

    private UUID itemCostChangeRequestId;
    
    private UUID itemAdjustmentRequestDetailId;

    private UUID itemCostCollectionId;
}
