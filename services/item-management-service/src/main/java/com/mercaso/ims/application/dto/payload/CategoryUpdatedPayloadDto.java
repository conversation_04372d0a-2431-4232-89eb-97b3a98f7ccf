package com.mercaso.ims.application.dto.payload;

import com.mercaso.ims.application.dto.CategoryDto;
import com.mercaso.ims.infrastructure.event.BusinessEventPayloadDto;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CategoryUpdatedPayloadDto extends BusinessEventPayloadDto<CategoryDto> {

    private UUID categoryId;

    @Builder
    public CategoryUpdatedPayloadDto(CategoryDto data, UUID categoryId) {
        super(data);
        this.categoryId = categoryId;
    }
}