package com.mercaso.ims.domain.attribute.service;

import com.mercaso.ims.domain.attribute.Attribute;
import java.util.List;
import java.util.UUID;

public interface AttributeService {

    Attribute findById(UUID id);

    List<Attribute> findByIds(List<UUID> ids);

    List<Attribute> findByName(String name);

    Attribute findByCategoryIdAndName(UUID categoryId, String name);

    List<Attribute> findByCategoryId(UUID categoryId);


    Attribute save(Attribute attribute);

    Attribute update(Attribute attribute);

    void delete(UUID id);

    List<Attribute> findAll();

    List<Attribute> findByFuzzyName(String name);


}
