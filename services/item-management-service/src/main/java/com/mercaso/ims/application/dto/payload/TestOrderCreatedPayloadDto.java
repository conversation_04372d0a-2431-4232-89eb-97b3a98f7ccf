package com.mercaso.ims.application.dto.payload;

import com.mercaso.ims.application.dto.TestOrderDto;
import com.mercaso.ims.infrastructure.event.BusinessEventPayloadDto;
import lombok.*;

import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class TestOrderCreatedPayloadDto extends BusinessEventPayloadDto<TestOrderDto> {

    private UUID testOrderId;

    @Builder
    public TestOrderCreatedPayloadDto(TestOrderDto data, UUID testOrderId) {
        super(data);
        this.testOrderId = testOrderId;
    }
}