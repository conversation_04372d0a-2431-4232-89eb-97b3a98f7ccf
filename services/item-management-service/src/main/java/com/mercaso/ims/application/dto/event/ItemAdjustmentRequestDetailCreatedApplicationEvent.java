package com.mercaso.ims.application.dto.event;

import com.mercaso.ims.application.dto.payload.ItemAdjustmentRequestDetailCreatedPayloadDto;
import com.mercaso.ims.infrastructure.event.applicationevent.BaseApplicationEvent;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class ItemAdjustmentRequestDetailCreatedApplicationEvent extends BaseApplicationEvent<ItemAdjustmentRequestDetailCreatedPayloadDto> {

    public ItemAdjustmentRequestDetailCreatedApplicationEvent(Object source, ItemAdjustmentRequestDetailCreatedPayloadDto payload) {
        super(source, payload);
    }
}

