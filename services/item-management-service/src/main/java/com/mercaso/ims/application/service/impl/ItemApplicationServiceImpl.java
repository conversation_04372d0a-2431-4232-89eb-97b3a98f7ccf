package com.mercaso.ims.application.service.impl;

import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.BRAND_NOT_FOUND;
import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.CATEGORY_SHOULD_BE_NOT_NULL;
import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.INVALID_BACKUP_VENDOR;
import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.INVALID_PRIMARY_JIT_VENDOR;
import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.INVALID_PRIMARY_PO_VENDOR;
import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.INVALID_SKU_NUMBER;
import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.ITEM_ALREADY_EXIST;
import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.ITEM_BINDING_PHOTO_ERROR;
import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.ITEM_NOT_FOUND;
import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.PRIMARY_VENDOR_ITEM_NOT_FOUND;
import static org.springframework.transaction.annotation.Propagation.REQUIRES_NEW;

import com.alibaba.excel.util.DateUtils;
import com.google.common.collect.Lists;
import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.ims.application.command.BatchUpdateItemPhotoCommand;
import com.mercaso.ims.application.command.BatchUpdateItemStatusCommand;
import com.mercaso.ims.application.command.CleanItemUpcCommand;
import com.mercaso.ims.application.command.CreateItemCommand;
import com.mercaso.ims.application.command.CreateItemPromoPriceCommand;
import com.mercaso.ims.application.command.CreateVendorItemCommand;
import com.mercaso.ims.application.command.DeleteItemCommand;
import com.mercaso.ims.application.command.UpdateItemBackupVendorCommand;
import com.mercaso.ims.application.command.UpdateItemCommand;
import com.mercaso.ims.application.command.UpdateItemPrimaryVendorCommand;
import com.mercaso.ims.application.command.UpdateItemPromoPriceCommand;
import com.mercaso.ims.application.command.UpdateItemUpcCommand;
import com.mercaso.ims.application.command.ValidateBarcodeCommand;
import com.mercaso.ims.application.dto.BatchUpdateItemPhotoResultDto;
import com.mercaso.ims.application.dto.BatchUpdateItemPromoPriceResultDto;
import com.mercaso.ims.application.dto.BatchUpdateItemStatusResultDto;
import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.application.dto.ItemImageDto;
import com.mercaso.ims.application.dto.ItemPromoPriceDto;
import com.mercaso.ims.application.dto.ItemRegPriceDto;
import com.mercaso.ims.application.dto.ItemTagDto;
import com.mercaso.ims.application.dto.ItemUPCDto;
import com.mercaso.ims.application.dto.PriceDto;
import com.mercaso.ims.application.dto.ValidateBarcodeResultDto;
import com.mercaso.ims.application.dto.VendorItemDto;
import com.mercaso.ims.application.dto.payload.ItemAmendPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemCreatedPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemDeletedPayloadDto;
import com.mercaso.ims.application.mapper.item.ItemDtoApplicationMapper;
import com.mercaso.ims.application.queryservice.ItemQueryApplicationService;
import com.mercaso.ims.application.service.DocumentApplicationService;
import com.mercaso.ims.application.service.ItemApplicationService;
import com.mercaso.ims.application.service.ItemPriceApplicationService;
import com.mercaso.ims.application.service.VendorItemApplicationService;
import com.mercaso.ims.domain.brand.Brand;
import com.mercaso.ims.domain.brand.service.BrandService;
import com.mercaso.ims.domain.businessevent.service.BusinessEventService;
import com.mercaso.ims.domain.item.Item;
import com.mercaso.ims.domain.item.ItemFactory;
import com.mercaso.ims.domain.item.ItemImage;
import com.mercaso.ims.domain.item.ItemUPC;
import com.mercaso.ims.domain.item.enums.AvailabilityStatus;
import com.mercaso.ims.domain.item.enums.ImageType;
import com.mercaso.ims.domain.item.enums.ItemUpcType;
import com.mercaso.ims.domain.item.service.ItemService;
import com.mercaso.ims.domain.itemattribute.ItemAttribute;
import com.mercaso.ims.domain.itempromoprice.ItemPromoPrice;
import com.mercaso.ims.domain.itempromoprice.ItemPromoPriceFactory;
import com.mercaso.ims.domain.itempromoprice.service.ItemPromoPriceService;
import com.mercaso.ims.domain.itemregprice.ItemRegPrice;
import com.mercaso.ims.domain.itemregprice.service.ItemRegPriceService;
import com.mercaso.ims.domain.vendor.VendorSpecification;
import com.mercaso.ims.domain.vendoritem.VendorItem;
import com.mercaso.ims.domain.vendoritem.enums.VendorItemType;
import com.mercaso.ims.domain.vendoritem.service.VendorItemService;
import com.mercaso.ims.infrastructure.config.metrics.MetricsTypeEnum;
import com.mercaso.ims.infrastructure.config.metrics.ReportMetric;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.util.BarcodeValidator;
import com.mercaso.ims.infrastructure.util.FileUtil;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

@Service
@Slf4j
@RequiredArgsConstructor
@Transactional
public class ItemApplicationServiceImpl implements ItemApplicationService {

    private final BusinessEventService businessEventService;
    private final ItemService itemService;
    private final ItemDtoApplicationMapper itemDtoApplicationMapper;

    private final ItemRegPriceService itemRegPriceService;
    private final ItemPromoPriceService itemPromoPriceService;
    private final DocumentApplicationService documentApplicationService;
    private final BrandService brandService;

    private final ItemQueryApplicationService itemQueryApplicationService;
    private final VendorItemService vendorItemService;
    private final VendorItemApplicationService vendorItemApplicationService;
    private final VendorSpecification vendorSpecification;
    private final ItemPriceApplicationService itemPriceApplicationService;

    @Override
    public ItemDto create(CreateItemCommand createItemCommand) {
        if (StringUtils.isBlank(createItemCommand.getSkuNumber())) {
            throw new ImsBusinessException(INVALID_SKU_NUMBER);
        }
        Item item = itemService.findBySku(createItemCommand.getSkuNumber());
        if (item != null) {
            throw new ImsBusinessException(ITEM_ALREADY_EXIST);
        }
        if (!vendorSpecification.isSatisfiedBackupVendor(createItemCommand.getBackupVendorId())) {
            throw new ImsBusinessException(INVALID_BACKUP_VENDOR);
        }

        String brandName = null;
        if (null != createItemCommand.getBrandId()) {
            brandName = fetchBrandOrThrow(createItemCommand.getBrandId()).getName();
        }
        item = ItemFactory.createItem(createItemCommand);
        item.setItemDefaultTags(brandName);
        List<String> itemTags = createItemCommand.getItemTags() == null ? null
            : createItemCommand.getItemTags().stream().map(ItemTagDto::getTagName).toList();
        item.addTagsIfNotExists(itemTags);
        String itemPhoto = getItemPhoto(item.getSkuNumber(), createItemCommand.getPhotoUrl(), createItemCommand.getPhotoName());
        if (itemPhoto != null) {
            item.setPhoto(itemPhoto);
        }
        updateItemImages(createItemCommand.getItemImages(), item, itemPhoto);
        item = itemService.save(item);

        createItemPrice(item.getId(),
            createItemCommand.getRegPrice(),
            createItemCommand.getCrvFlag(),
            item.getPackageSize(),
            item.getItemBottleSizeAttribute(),
            createItemCommand.getItemPromoPrices());

        createVendorItems(createItemCommand.getVendorItems(), item, createItemCommand.getItemAdjustmentRequestDetailId());
        setDefaultBackupVendorItem(item.getId(), item.getBackupVendorId());
        ItemDto itemDto = itemQueryApplicationService.findById(item.getId());
        businessEventService.dispatch(ItemCreatedPayloadDto.builder()
            .itemId(itemDto.getId())
            .itemAdjustmentRequestDetailId(createItemCommand.getItemAdjustmentRequestDetailId())
            .data(itemDto)
            .build());
        return itemDto;
    }

    @Override
    public ItemDto update(UpdateItemCommand updateItemCommand) {
        Item item;
        if (updateItemCommand.getId() != null) {
            item = fetchItemOrThrow(updateItemCommand.getId());
        } else {
            log.info("Update item by sku: {}", updateItemCommand.getSkuNumber());
            item = fetchItemBySkuOrThrow(updateItemCommand.getSkuNumber());
        }
        if (!vendorSpecification.isSatisfiedBackupVendor(updateItemCommand.getBackupVendorId())) {
            throw new ImsBusinessException(INVALID_BACKUP_VENDOR);
        }
        ItemDto previous = itemQueryApplicationService.findById(item.getId());

        if (updateItemCommand.getItemAttributes() != null) {
            List<ItemAttribute> attributes = new ArrayList<>();
            updateItemCommand.getItemAttributes().forEach(
                itemAttributeDto -> attributes.add(ItemAttribute.builder()
                    .attributeId(itemAttributeDto.getAttributeId())
                    .attributeType(itemAttributeDto.getAttributeType())
                    .unit(itemAttributeDto.getUnit())
                    .value(itemAttributeDto.getValue())
                    .build())
            );
            item.updateItemAttributes(attributes);
        }

        if (updateItemCommand.getItemUPCs() != null) {
            List<ItemUPC> itemUPCs = convertToItemUPCs(updateItemCommand.getItemUPCs());
            item.updateItemUPCs(itemUPCs);
        }
        item = item.update(updateItemCommand);

        String itemPhoto = getItemPhoto(item.getSkuNumber(), updateItemCommand.getPhotoUrl(), updateItemCommand.getPhotoName());
        if (itemPhoto != null) {
            item.setPhoto(itemPhoto);
        }
        List<String> newTags = updateItemCommand.getItemTags() == null ? null
            : updateItemCommand.getItemTags().stream().map(ItemTagDto::getTagName).toList();
        item.updateItemTags(newTags);
        updateItemImages(updateItemCommand.getItemImages(), item, itemPhoto);
        setDefaultBackupVendorItem(item.getId(), item.getBackupVendorId());
        item = itemService.save(item);
        ItemAttribute itemBottleSizeAttribute = item.getItemBottleSizeAttribute();
        ItemRegPrice regPrice = itemRegPriceService.update(item.getId(),
            updateItemCommand.getRegPrice(),
            updateItemCommand.getCrvFlag(),
            item.getPackageSize(),
            itemBottleSizeAttribute);

        updateItemPromoPrice(
            item.getId(),
            item.getPackageSize(),
            regPrice.getCrvFlag(),
            regPrice.getCrv(),
            updateItemCommand.getItemPromoPrices()
        );

        ItemDto current = itemQueryApplicationService.findById(item.getId());
        businessEventService.dispatch(ItemAmendPayloadDto.builder()
            .itemId(item.getId())
            .itemAdjustmentRequestDetailId(updateItemCommand.getItemAdjustmentRequestDetailId())
            .previous(previous)
            .current(current)
            .build());
        return current;

    }

    @Override
    public ItemDto deleteItemById(DeleteItemCommand deleteItemCommand) {
        Item item = fetchItemOrThrow(deleteItemCommand.getId());
        item = itemService.delete(item.getId());
        ItemDto dto = itemQueryApplicationService.findById(deleteItemCommand.getId());

        if (dto.getItemRegPrice() != null && dto.getItemRegPrice().getItemPriceGroupId() != null) {
            itemPriceApplicationService.unbindingItemPriceGroup(dto.getId(), dto.getItemRegPrice().getItemPriceGroupId());
        }
        businessEventService.dispatch(ItemDeletedPayloadDto.builder()
            .itemId(item.getId())
            .itemAdjustmentRequestDetailId(deleteItemCommand.getItemAdjustmentRequestDetailId())
            .data(dto)
            .build());
        return dto;
    }

    public void updateItemPromoPrice(UUID itemId, Integer packageSize,
        Boolean crvFlag,
        BigDecimal crv, List<ItemPromoPriceDto> itemPromoPrices) {

        if (CollectionUtils.isNotEmpty(itemPromoPrices)) {
            ItemPromoPriceDto promoPrice = itemPromoPrices.getFirst();
            updateItemPromoPrice(itemId,
                promoPrice.getPromoPrice(),
                promoPrice.getPromoFlag(),
                promoPrice.getPromoBeginTime(),
                promoPrice.getPromoEndTime(),
                packageSize,
                crvFlag,
                crv);
        }
    }


    private void updateItemPromoPrice(UUID itemId,
        BigDecimal promoPrice,
        Boolean promoFlag,
        Instant promoBeginTime,
        Instant promoEndTime,
        Integer packageSize,
        Boolean crvFlag,
        BigDecimal crv) {
        ItemPromoPrice itemPromoPrice = itemPromoPriceService.findByItemId(itemId).stream().findFirst().orElse(null);

        if (itemPromoPrice == null && promoPrice == null) {
            log.warn("ItemPromoPrice not found as updateItemPromoPrice id: {}", itemId);
            return;
        }

        promoPrice = (promoPrice != null) ? promoPrice
            : Optional.ofNullable(itemPromoPrice).map(ItemPromoPrice::getPromoPrice).orElse(null);

        if (itemPromoPrice == null) {
            itemPromoPriceService.save(ItemPromoPriceFactory.create(CreateItemPromoPriceCommand.builder()
                .itemId(itemId)
                .promoPrice(promoPrice)
                .promoFlag(promoFlag)
                .promoBeginTime(promoBeginTime)
                .promoEndTime(promoEndTime)
                .build(), packageSize, crvFlag, crv));
        } else {
            PriceDto priceDto = new PriceDto(promoPrice, packageSize, crvFlag, crv);
            itemPromoPrice.updatePrice(priceDto);
            if (promoFlag != null) {
                itemPromoPrice.setPromoFlag(promoFlag);
            }
            itemPromoPrice.setPromoBeginTime(promoBeginTime);
            itemPromoPrice.setPromoEndTime(promoEndTime);

            itemPromoPriceService.save(itemPromoPrice);
        }
    }

    @Override
    @ReportMetric(metricsType = MetricsTypeEnum.ITEM_BINDING_PHOTO)
    public ItemDto bindingPhoto(UUID id, MultipartFile file) {
        ItemDto previous = itemQueryApplicationService.findById(id);
        Item item = fetchItemOrThrow(id);
        log.info("[bindingPhoto] Binding photo for item: {} ", id);
        try {
            String itemPhoto = getItemPhoto(item.getSkuNumber(), file.getBytes());
            item.setPhoto(itemPhoto);
            item.updateItemImages(Lists.newArrayList(ItemImage.builder()
                .itemId(item.getId())
                .fileName(itemPhoto)
                .imageType(ImageType.MAIN_IMAGE)
                .build()));
            item = itemService.save(item);
            ItemDto current = itemQueryApplicationService.findById(item.getId());
            businessEventService.dispatch(ItemAmendPayloadDto.builder()
                .itemId(item.getId())
                .previous(previous)
                .current(current)
                .build());
            log.info("[bindingPhoto] end binding photo for itemDto: {} ", current);
            return current;
        } catch (Exception e) {
            log.error("Error binding photo for item: {} ", id, e);
            throw new ImsBusinessException(ITEM_BINDING_PHOTO_ERROR);
        }
    }

    @Override
    public void cleanItemUPCs(CleanItemUpcCommand command) {
        Item item = fetchItemOrThrow(command.getId());
        ItemDto previous = itemQueryApplicationService.findById(item.getId());

        item.cleanItemUPCs();
        item = itemService.save(item);
        ItemDto current = itemQueryApplicationService.findById(item.getId());
        businessEventService.dispatch(ItemAmendPayloadDto.builder()
            .itemId(item.getId())
            .itemAdjustmentRequestDetailId(command.getItemAdjustmentRequestDetailId())
            .previous(previous)
            .current(current)
            .build());
    }

    @Override
    public ItemDto active(UUID id) {
        return updateItemStatus(id, AvailabilityStatus.ACTIVE);

    }

    @Override
    public ItemDto draft(UUID id) {
        return updateItemStatus(id, AvailabilityStatus.DRAFT);

    }

    @Override
    public ItemDto archive(UUID id) {
        return updateItemStatus(id, AvailabilityStatus.ARCHIVED);
    }

    @Override
    public BatchUpdateItemStatusResultDto batchUpdateItemStatus(BatchUpdateItemStatusCommand command) {
        if (CollectionUtils.isEmpty(command.getItemIds())) {
            log.warn("No item ids provided for batch update item status");
            return BatchUpdateItemStatusResultDto.builder()
                .updatedCount(0)
                .failedSkuNumbers(Collections.emptyList())
                .build();
        }

        List<String> failedSkuNumbers = new ArrayList<>();
        command.getItemIds().forEach(id -> {
            try {
                updateItemStatus(id, command.getStatus());
            } catch (Exception e) {
                log.error("Failed to update status for item {}", id, e);
                failedSkuNumbers.add(getSkuNumberForFailedItem(id));
            }
        });

        int updatedCount = command.getItemIds().size() - failedSkuNumbers.size();
        return BatchUpdateItemStatusResultDto.builder()
            .updatedCount(updatedCount)
            .failedSkuNumbers(failedSkuNumbers)
            .build();
    }

    @Override
    public ValidateBarcodeResultDto validateBarcode(ValidateBarcodeCommand command) {
        boolean isValid = BarcodeValidator.isValidBarcode(command.getBarcode());
        if (isValid) {
            List<Item> items = itemService.findAllByUpcAndUpcType(command.getBarcode(), ItemUpcType.valueOf(command.getType()));
            List<ItemDto> itemDto = items.stream().map(itemDtoApplicationMapper::domainToDto).toList();
            return ValidateBarcodeResultDto.builder()
                .isValid(true)
                .message("UPC is valid")
                .associatedItems(itemDto)
                .build();
        }
        return ValidateBarcodeResultDto.builder()
            .isValid(false)
            .message("Invalid UPC number")
            .build();
    }

    @Override
    public BatchUpdateItemPhotoResultDto batchUpdateItemPhoto(List<BatchUpdateItemPhotoCommand> commands) {
        if (CollectionUtils.isEmpty(commands)) {
            log.warn("No item ids provided for batch update item photo");
            return BatchUpdateItemPhotoResultDto.builder()
                .updatedCount(0)
                .failedSkuNumbers(Collections.emptyList())
                .build();
        }

        List<String> failedSkuNumbers = new ArrayList<>();
        commands.forEach(command -> {
            ItemDto previous = null;
            try {
                previous = itemQueryApplicationService.findById(command.getItemId());
                Item item = fetchItemOrThrow(command.getItemId());
                updateItemImages(Lists.newArrayList(), item, command.getPhotoName());
                item.setPhoto(command.getPhotoName());
                itemService.save(item);
                ItemDto current = itemQueryApplicationService.findById(command.getItemId());
                businessEventService.dispatch(ItemAmendPayloadDto.builder()
                    .itemId(command.getItemId())
                    .previous(previous)
                    .current(current)
                    .build());
            } catch (Exception e) {
                log.error("Failed to update status for item {}", command.getItemId(), e);
                if (previous != null) {
                    failedSkuNumbers.add(previous.getSkuNumber());
                } else {
                    failedSkuNumbers.add(command.getItemId().toString());
                }

            }
        });

        int updatedCount = commands.size() - failedSkuNumbers.size();
        return BatchUpdateItemPhotoResultDto.builder()
            .updatedCount(updatedCount)
            .failedSkuNumbers(failedSkuNumbers)
            .build();
    }

    @Override
    public ItemDto updatePromoPrice(UpdateItemPromoPriceCommand command) {
        return updatePromoPriceAndSendEvent(command);
    }

    @Override
    public ItemDto updateItemUpc(UpdateItemUpcCommand command) {
        Item item = fetchItemOrThrow(command.getItemId());

        ItemDto previous = itemQueryApplicationService.findById(command.getItemId());
        List<ItemUPC> itemUPCs = convertToItemUPCs(command.getItemUPCs());
        item.updateItemUPCs(itemUPCs);
        itemService.save(item);
        ItemDto current = itemQueryApplicationService.findById(command.getItemId());
        businessEventService.dispatch(ItemAmendPayloadDto.builder()
            .itemId(command.getItemId())
            .previous(previous)
            .current(current)
            .build());
        return current;
    }

    @Override
    @Transactional(propagation = REQUIRES_NEW)
    public ItemDto refreshPrimaryBackupVendor(UUID id) {

        Item item = fetchItemOrThrow(id);
        ItemDto previous = itemQueryApplicationService.findById(id);
        log.info("Refresh JIT primary vendor for item: {} ", id);

        UUID primaryVendorId = refreshPrimaryVendorId(previous);

        UUID backupVendorId = refreshBackupVendorId(previous);
        boolean primaryChanged = !Objects.equals(previous.getPrimaryVendorId(), primaryVendorId);
        boolean backupChanged = !Objects.equals(previous.getBackupVendorId(), backupVendorId);
        if (primaryChanged || backupChanged) {
            item.setPrimaryVendorId(primaryVendorId);
            item.setBackupVendorId(backupVendorId);
            item = itemService.save(item);

            ItemDto current = itemQueryApplicationService.findById(item.getId());
            businessEventService.dispatch(ItemAmendPayloadDto.builder()
                .itemId(item.getId())
                .previous(previous)
                .current(current)
                .build());
            return current;
        }
        return previous;

    }

    @Override
    public BatchUpdateItemPromoPriceResultDto batchUpdatePromoPrice(List<UpdateItemPromoPriceCommand> commands) {
        List<String> failedSkuNumbers = new ArrayList<>();

        for (UpdateItemPromoPriceCommand command : commands) {
            try {
                updatePromoPriceAndSendEvent(command);
            } catch (Exception e) {
                log.error("Failed to update promo price for item {}", command.getItemId(), e);
                failedSkuNumbers.add(getSkuNumberForFailedItem(command.getItemId()));
            }
        }

        int updatedCount = commands.size() - failedSkuNumbers.size();
        return BatchUpdateItemPromoPriceResultDto.builder()
            .updatedCount(updatedCount)
            .failedSkuNumbers(failedSkuNumbers)
            .build();
    }


    private ItemDto updateItemStatus(UUID id, AvailabilityStatus status) {
        Item item = Optional.ofNullable(itemService.findById(id))
            .orElseThrow(() -> new ImsBusinessException(ITEM_NOT_FOUND.getCode()));
        ItemDto previous = itemQueryApplicationService.findById(item.getId());

        log.info("Update item status for item: {} to {}", id, status);
        UUID categoryId = previous.getCategoryId();
        if (null == categoryId) {
            throw new ImsBusinessException(CATEGORY_SHOULD_BE_NOT_NULL);
        }

        switch (status) {
            case ACTIVE:
                item.active();
                break;
            case DRAFT:
                item.draft();
                break;
            case ARCHIVED:
                item.archive();
                break;
            default:
                throw new IllegalArgumentException("Unsupported status: " + status);
        }
        item = itemService.save(item);
        ItemDto current = itemQueryApplicationService.findById(item.getId());
        businessEventService.dispatch(ItemAmendPayloadDto.builder()
            .itemId(item.getId())
            .previous(previous)
            .current(current)
            .build());
        return current;
    }


    @Override
    public ItemDto updatePrimaryVendor(UpdateItemPrimaryVendorCommand command) {
        ItemDto previous = itemQueryApplicationService.findById(command.getId());
        Item item = fetchItemOrThrow(command.getId());
        if (null != command.getPrimaryVendorId()) {
            VendorItem primaryVendorItem = vendorItemService.findByVendorIDAndItemId(command.getPrimaryVendorId(),
                command.getId());
            if (primaryVendorItem == null) {
                throw new ImsBusinessException(PRIMARY_VENDOR_ITEM_NOT_FOUND);
            }
            if (VendorItemType.JIT.getTypeName()
                .equals(primaryVendorItem.getVendorItemType())) {
                throw new ImsBusinessException(INVALID_PRIMARY_PO_VENDOR);
            }
        }

        item.setPrimaryVendorId(command.getPrimaryVendorId());
        item = itemService.save(item);
        ItemDto current = itemQueryApplicationService.findById(item.getId());
        businessEventService.dispatch(ItemAmendPayloadDto.builder()
            .itemId(item.getId())
            .previous(previous)
            .current(current)
            .build());
        return current;
    }

    @Override
    public ItemDto updateBackupVendor(UpdateItemBackupVendorCommand command) {
        ItemDto previous = itemQueryApplicationService.findById(command.getId());

        if (command.getBackupVendorId() != null) {
            VendorItem backupVendorItem = vendorItemService.findByVendorIDAndItemId(command.getBackupVendorId(),
                command.getId());
            if (backupVendorItem == null || VendorItemType.DIRECT.getTypeName()
                .equals(backupVendorItem.getVendorItemType())) {
                throw new ImsBusinessException(INVALID_PRIMARY_JIT_VENDOR);
            }
        }

        Item item = fetchItemOrThrow(command.getId());
        item.setBackupVendorId(command.getBackupVendorId());
        item = itemService.save(item);
        setDefaultBackupVendorItem(item.getId(), item.getBackupVendorId());
        ItemDto current = itemQueryApplicationService.findById(item.getId());
        businessEventService.dispatch(ItemAmendPayloadDto.builder()
            .itemId(item.getId())
            .previous(previous)
            .current(current)
            .build());
        return current;
    }

    private Item fetchItemOrThrow(UUID id) {
        return Optional.ofNullable(itemService.findById(id))
            .orElseThrow(() -> new ImsBusinessException(ITEM_NOT_FOUND));
    }

    private Item fetchItemBySkuOrThrow(String sku) {
        return Optional.ofNullable(itemService.findBySku(sku))
            .orElseThrow(() -> new ImsBusinessException(ITEM_NOT_FOUND));
    }

    private String getItemPhoto(String sku, String externalImageUrl, String photoName) {
        if (StringUtils.isNotBlank(photoName) && StringUtils.isNotBlank(documentApplicationService.getSignedUrl(photoName))) {
            return photoName;
        }

        if (StringUtils.isBlank(externalImageUrl)) {
            return null;
        }
        byte[] docBytes = FileUtil.downloadFile(externalImageUrl);
        return getItemPhoto(sku, docBytes);
    }

    private String getItemPhoto(String sku, byte[] fileBytes) {
        String fileExtension = FileUtil.getFileExtension(fileBytes);
        if (StringUtils.isBlank(fileExtension)) {
            log.error("[getItemPhoto] Unable to get imageFormat item: {}  ", sku);
            return null;
        }
        String filename = sku.concat("-")
            .concat(DateUtils.format(new Date(), DateUtils.DATE_FORMAT_14))
            .concat(fileExtension);
        DocumentResponse documentResponse = documentApplicationService.uploadImage(fileBytes, filename);
        return documentResponse.getName();
    }


    private String determineVendorItemType(VendorItemDto vendorItemDto, boolean primaryAndBackupSame, UUID primaryVendorId) {
        // If vendor item type is already set, use it
        if (vendorItemDto.getVendorItemType() != null) {
            return vendorItemDto.getVendorItemType();
        }

        // If primary and backup vendors are the same
        if (primaryAndBackupSame) {
            return VendorItemType.DIRECT_JIT.getTypeName();
        }

        // Determine type based on whether this is primary vendor
        return vendorItemDto.getVendorId().equals(primaryVendorId)
            ? VendorItemType.DIRECT.getTypeName()
            : VendorItemType.JIT.getTypeName();
    }

    private void createVendorItems(List<VendorItemDto> vendorItemDtos, Item item, UUID itemAdjustmentRequestDetailId) {
        if (CollectionUtils.isNotEmpty(vendorItemDtos)) {
            boolean primaryAndBackupSame = Objects.equals(item.getPrimaryVendorId(), item.getBackupVendorId());
            vendorItemDtos.forEach(vendorItemDto -> vendorItemApplicationService.create(CreateVendorItemCommand.builder()
                .vendorId(vendorItemDto.getVendorId())
                .itemId(item.getId())
                .vendorSkuNumber(vendorItemDto.getVendorSkuNumber())
                .aisle(vendorItemDto.getAisle())
                .cost(vendorItemDto.getCost())
                .backupCost(vendorItemDto.getBackupCost())
                .vendorItemType(determineVendorItemType(vendorItemDto, primaryAndBackupSame, item.getPrimaryVendorId()))
                .itemAdjustmentRequestDetailId(itemAdjustmentRequestDetailId)
                .availability(vendorItemDto.getAvailability())
                .build()));
        }
    }


    private void createItemPromoPrice(UUID itemId,
        List<ItemPromoPriceDto> itemPromoPrices,
        Integer packSize,
        Boolean crvFlag,
        BigDecimal crv) {
        if (CollectionUtils.isNotEmpty(itemPromoPrices)) {
            ItemPromoPriceDto promoPrice = itemPromoPrices.getFirst();

            itemPromoPriceService.save(ItemPromoPriceFactory.create(CreateItemPromoPriceCommand.builder()
                .itemId(itemId)
                .promoPrice(promoPrice.getPromoPrice())
                .promoFlag(promoPrice.getPromoFlag())
                .promoBeginTime(promoPrice.getPromoBeginTime())
                .promoEndTime(promoPrice.getPromoEndTime())
                .build(), packSize, crvFlag, crv));
        }
    }

    private Brand fetchBrandOrThrow(UUID brandId) {
        return Optional.ofNullable(brandService.findById(brandId))
            .orElseThrow(() -> new ImsBusinessException(BRAND_NOT_FOUND));
    }

    private void updateItemImages(List<ItemImageDto> itemImageDtos, Item item, String itemPhoto) {
        if (CollectionUtils.isEmpty(itemImageDtos)) {
            itemImageDtos = new ArrayList<>();
        }

        if (StringUtils.isNotBlank(itemPhoto) && itemImageDtos.stream()
            .noneMatch(itemImageDto -> itemImageDto.getImageType().equals(ImageType.MAIN_IMAGE))) {
            itemImageDtos.add(ItemImageDto.builder()
                .fileName(itemPhoto)
                .imageType(ImageType.MAIN_IMAGE)
                .build());
        }

        List<ItemImage> itemImages = new ArrayList<>();
        itemImageDtos.forEach(
            itemImageDto -> itemImages.add(ItemImage.builder()
                .fileName(itemImageDto.getFileName())
                .imageType(itemImageDto.getImageType())
                .sort(itemImageDto.getSort())
                .status(itemImageDto.getStatus())
                .build())
        );
        item.updateItemImages(itemImages);
    }

    private void setDefaultBackupVendorItem(UUID itemId, UUID backupVendorId) {
        if (backupVendorId != null) {
            VendorItem backupVendorItem = vendorItemService.findByVendorIDAndItemId(backupVendorId, itemId);
            if (backupVendorItem == null) {
                vendorItemApplicationService.create(CreateVendorItemCommand.builder()
                    .vendorId(backupVendorId)
                    .itemId(itemId)
                    .build());
            }
        }
    }

    private void createItemPrice(UUID itemId,
        BigDecimal regPrice,
        Boolean crvFlag,
        Integer packageSize,
        ItemAttribute bottleSizeAttribute, List<ItemPromoPriceDto> itemPromoPrices) {
        ItemRegPrice itemRegPrice = itemRegPriceService.save(itemId,
            regPrice,
            crvFlag,
            packageSize,
            bottleSizeAttribute);
        createItemPromoPrice(itemId,
            itemPromoPrices,
            packageSize,
            itemRegPrice.getCrvFlag(),
            itemRegPrice.getCrv()
        );

    }

    private List<ItemUPC> convertToItemUPCs(List<ItemUPCDto> targetItemUPCs) {
        List<ItemUPC> itemUPCs = new ArrayList<>();

        targetItemUPCs.stream().filter(itemUPCDto -> StringUtils.isNotBlank(itemUPCDto.getUpcNumber()))
            .forEach(
                itemUPCDto -> itemUPCs.add(ItemUPC.builder()
                    .upcNumber(itemUPCDto.getUpcNumber().trim())
                    .itemUpcType(itemUPCDto.getItemUpcType())
                    .build())
            );
        return itemUPCs;
    }

    private VendorItem getLowestJitCostVendorItem(UUID itemId) {
        List<VendorItem> vendorItems = vendorItemService.findByItemID(itemId);
        if (CollectionUtils.isEmpty(vendorItems)) {
            return null;
        }

        Instant monthAgo = Instant.now().minus(28, ChronoUnit.DAYS);

        return vendorItems.stream()
            .filter(v -> isValidJitVendorItem(v, monthAgo))
            .min(Comparator.comparing(VendorItem::getBackupPackPlusCrvCost))
            .orElse(null);
    }

    private boolean isValidJitVendorItem(VendorItem v, Instant cutoffTime) {
        return v.getVendorItemType() != null &&
            (VendorItemType.JIT.getTypeName().equals(v.getVendorItemType()) ||
                VendorItemType.DIRECT_JIT.getTypeName().equals(v.getVendorItemType())) &&
            Boolean.TRUE.equals(v.getAvailability()) &&
            v.getBackupPackPlusCrvCost() != null &&
            v.getBackupCostFreshnessTime() != null &&
            v.getBackupCostFreshnessTime().isAfter(cutoffTime);
    }


    private ItemDto updatePromoPriceAndSendEvent(UpdateItemPromoPriceCommand command) {
        ItemDto previous = itemQueryApplicationService.findById(command.getItemId());

        ItemRegPriceDto itemPromoPrice = previous.getItemRegPrice();
        updateItemPromoPrice(
            previous.getId(),
            command.getPromoPrice(),
            command.getPromoFlag(),
            command.getPromoBeginTime(),
            command.getPromoEndTime(),
            previous.getPackageSize(),
            itemPromoPrice.getCrvFlag(),
            itemPromoPrice.getCrv()
        );

        // Send event after price update
        ItemDto current = itemQueryApplicationService.findById(command.getItemId());
        businessEventService.dispatch(ItemAmendPayloadDto.builder()
            .itemId(command.getItemId())
            .previous(previous)
            .current(current)
            .build());
        return current;
    }

    private String getSkuNumberForFailedItem(UUID itemId) {
        try {
            ItemDto failedItem = itemQueryApplicationService.findById(itemId);
            return failedItem != null ? failedItem.getSkuNumber() : itemId.toString();
        } catch (Exception e) {
            return itemId.toString();
        }
    }

    private UUID refreshPrimaryVendorId(ItemDto item) {
        UUID primaryVendorId = item.getPrimaryVendorId();
        List<VendorItemDto> vendorItems = item.getVendorItemDtos();

        if (vendorItems == null || vendorItems.isEmpty() || vendorItems.stream().noneMatch(VendorItemDto::isDirectVendorItem)) {
            return null;
        }
        UUID finalPrimaryVendorId = primaryVendorId;
        if (finalPrimaryVendorId != null) {
            boolean isPrimaryVendorNowJit = vendorItems.stream()
                .anyMatch(v -> finalPrimaryVendorId.equals(v.getVendorId()) &&
                    VendorItemType.JIT.getTypeName().equals(v.getVendorItemType()));
            if (isPrimaryVendorNowJit) {
                primaryVendorId = null;
            }
        }

        if (primaryVendorId == null && vendorItems.stream().noneMatch(VendorItemDto::isJitVendorItem)) {
            Optional<VendorItemDto> validDirectVendor = vendorItems.stream()
                .filter(v -> v.isDirectVendorItem() && v.getVendorId() != null)
                .findFirst();
            return validDirectVendor.map(VendorItemDto::getVendorId).orElse(null);
        }
        return primaryVendorId;
    }


    private UUID refreshBackupVendorId(ItemDto item) {
        UUID backupVendorId = item.getBackupVendorId();
        VendorItem lowestCostJitVendorItem = getLowestJitCostVendorItem(item.getId());
        if (lowestCostJitVendorItem == null) {
            if (item.getVendorItemDtos().stream().noneMatch(VendorItemDto::isJitVendorItem)) {
                return null;
            }

            if (backupVendorId != null && !item.getBackupVendorItem().isJitVendorItem()) {
                backupVendorId = null;
            }
            if (backupVendorId == null && item.getPrimaryVendorId() == null) {
                Optional<VendorItemDto> validJitVendor = item.getVendorItemDtos().stream()
                    .filter(v -> v.isJitVendorItem() && v.getVendorId() != null)
                    .findFirst();
                return validJitVendor.map(VendorItemDto::getVendorId).orElse(null);
            }
            return item.getBackupVendorId();

        }
        if (item.getBackupVendorItem() != null && item.getBackupVendorItem().getBackupCost() != null) {
            if (lowestCostJitVendorItem.getBackupPackPlusCrvCost().compareTo(item.getBackupVendorItem().getBackupCost()) < 0) {
                log.info("Set backup vendor to {} for item: {} ", lowestCostJitVendorItem.getVendorId(), item);
                return lowestCostJitVendorItem.getVendorId();
            } else {
                return item.getBackupVendorId();
            }

        } else {
            return lowestCostJitVendorItem.getVendorId();
        }

    }

}
