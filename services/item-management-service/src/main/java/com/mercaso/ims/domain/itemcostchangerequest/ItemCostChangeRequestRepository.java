package com.mercaso.ims.domain.itemcostchangerequest;

import com.mercaso.ims.domain.BaseDomainRepository;
import java.util.List;
import java.util.UUID;

public interface ItemCostChangeRequestRepository extends BaseDomainRepository<ItemCostChangeRequest, UUID> {

    List<ItemCostChangeRequest> findByItemCostCollectionId(UUID itemCostCollectionId);

    List<ItemCostChangeRequest> findByItemCostCollectionIdIn(List<UUID> itemCostCollectionIds);
}
