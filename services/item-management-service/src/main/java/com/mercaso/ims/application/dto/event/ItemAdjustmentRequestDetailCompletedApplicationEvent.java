package com.mercaso.ims.application.dto.event;

import com.mercaso.ims.application.dto.payload.ItemAdjustmentRequestDetailCompletedPayloadDto;
import com.mercaso.ims.infrastructure.event.applicationevent.BaseApplicationEvent;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class ItemAdjustmentRequestDetailCompletedApplicationEvent extends BaseApplicationEvent<ItemAdjustmentRequestDetailCompletedPayloadDto> {

    public ItemAdjustmentRequestDetailCompletedApplicationEvent(Object source, ItemAdjustmentRequestDetailCompletedPayloadDto payload) {
        super(source, payload);
    }
}

