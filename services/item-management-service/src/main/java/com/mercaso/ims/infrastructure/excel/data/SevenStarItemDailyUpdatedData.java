package com.mercaso.ims.infrastructure.excel.data;

import com.alibaba.excel.annotation.ExcelProperty;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Slf4j
public class SevenStarItemDailyUpdatedData {


    @ExcelProperty("ItemCode")
    private String itemCode;
    @ExcelProperty("Item Description")
    private String itemDescription;
    @ExcelProperty("On Hand")
    private String onHand;
    @ExcelProperty("Avail Qty")
    private Integer availQty;
    @ExcelProperty("UOM")
    private String uom;
    @ExcelProperty("CP")
    private String slsCp;
    @ExcelProperty("UPC Code")
    private String upcCode;
    @ExcelProperty("Unit Sell Price")
    private String unitCost;

    @ExcelProperty("On PO")
    private String onPo;
    @ExcelProperty("Country")
    private String country;
    @ExcelProperty("Weight")
    private String weight;
    @ExcelProperty("Volume")
    private String volume;

    @ExcelProperty("ProductLineDesc")
    private String productLineDesc;
    @ExcelProperty("UDF_SUB_CAT_DESCRIP")
    private String udfSubCatDescrip;

    public BigDecimal getInnrPrice() {
        BigDecimal slsCpBigDecimal = convertToBigDecimal(slsCp);
        BigDecimal unitCostBigDecimal = convertToBigDecimal(unitCost);
        if (slsCpBigDecimal == null || unitCostBigDecimal == null) {
            return null;
        }
        return slsCpBigDecimal.multiply(unitCostBigDecimal);
    }


    private BigDecimal convertToBigDecimal(String value) {

        if (value == null || value.trim().isEmpty()) {
            return null;
        }
        value = value.replace("$", "").replace(",", "").trim();

        try {
            return new BigDecimal(value);
        } catch (NumberFormatException e) {
            log.warn("Failed to convert value to BigDecimal: {}", value, e);
            return null;
        }
    }

    public BigDecimal getInnrAvaNumber() {
        return convertToBigDecimal(onHand);
    }

}
