package com.mercaso.wms.interfaces;

import static com.mercaso.wms.domain.receivingtask.enums.ReceivingTaskStatus.CREATED;
import static com.mercaso.wms.utils.MockDataUtils.buildReceivingTask;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.command.receivingtask.UpdateReceivingTaskCommand;
import com.mercaso.wms.application.command.receivingtask.UpdateReceivingTaskCommand.UpdateReceivingTaskItem;
import com.mercaso.wms.application.dto.receivingtask.ReceivingTaskDto;
import com.mercaso.wms.application.dto.receivingtask.ReceivingTaskItemDto;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.receivingtask.ReceivingTask;
import com.mercaso.wms.domain.receivingtask.enums.ReceivingTaskStatus;
import com.mercaso.wms.domain.receivingtask.enums.ReceivingTaskType;
import com.mercaso.wms.utils.ReceivingTaskResourceApi;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class ReceivingTaskResourceIT extends AbstractIT {

    @Autowired
    private ReceivingTaskResourceApi receivingTaskResourceApi;

    @Test
    void when_scan_receive_item_id_then_return_receiving_task_item_dto() throws JsonProcessingException {
        receivingTaskRepository.deleteAll();
        List<ReceivingTask> receivingTasks = buildReceivingTask(UUID.randomUUID(),
            1,
            SourceEnum.VERNON,
            CREATED,
            ReceivingTaskType.ONLINE_RECEIVING);

        ReceivingTask result = receivingTaskRepository.save(receivingTasks.getFirst());

        ReceivingTaskItemDto receivingTaskItemDto = receivingTaskResourceApi.scanReceiveItem(result.getReceivingTaskItems()
            .getFirst()
            .getId());

        assertNotNull(receivingTaskItemDto);
        assertEquals(1, receivingTaskItemDto.getReceivedQty());

        ReceivingTask receivingTask = receivingTaskRepository.findById(result.getId());

        assertNotNull(receivingTask);
        assertEquals(ReceivingTaskStatus.RECEIVING, receivingTask.getStatus());

        receivingTask.getReceivingTaskItems().getFirst().setReceivedQty(9);
        receivingTask.getReceivingTaskItems().getLast().setReceivedQty(10);
        receivingTaskRepository.save(receivingTask);

        ReceivingTaskItemDto receivingTaskItemDto1 = receivingTaskResourceApi.scanReceiveItem(result.getReceivingTaskItems()
            .getFirst()
            .getId());

        assertNotNull(receivingTaskItemDto1);
        assertEquals(10, receivingTaskItemDto1.getReceivedQty());

        ReceivingTask receivingTask1 = receivingTaskRepository.findById(result.getId());

        assertNotNull(receivingTask1);
        assertEquals(ReceivingTaskStatus.RECEIVED, receivingTask1.getStatus());
    }

    @Test
    void when_receive_task_id_then_return_receiving_task_dto() throws JsonProcessingException {
        receivingTaskRepository.deleteAll();
        List<ReceivingTask> receivingTasks = buildReceivingTask(UUID.randomUUID(),
            1,
            SourceEnum.VERNON,
            CREATED,
            ReceivingTaskType.ONLINE_RECEIVING);

        ReceivingTask result = receivingTaskRepository.save(receivingTasks.getFirst());

        ReceivingTaskItemDto receivingTaskItemDto = receivingTaskResourceApi.scanReceiveItem(result.getReceivingTaskItems()
            .getFirst()
            .getId());

        assertNotNull(receivingTaskItemDto);
        assertEquals(1, receivingTaskItemDto.getReceivedQty());

        ReceivingTask receivingTask = receivingTaskRepository.findById(result.getId());

        assertNotNull(receivingTask);
        assertEquals(ReceivingTaskStatus.RECEIVING, receivingTask.getStatus());

        ReceivingTaskDto receivingTaskDto = receivingTaskResourceApi.receiveTask(result.getId());

        assertNotNull(receivingTaskDto);
        assertEquals(ReceivingTaskStatus.RECEIVED, receivingTaskDto.getStatus());
    }

    @Test
    void when_update_receiving_task_then_return_receiving_task_dto() throws JsonProcessingException {
        receivingTaskRepository.deleteAll();
        List<ReceivingTask> receivingTasks = buildReceivingTask(UUID.randomUUID(),
            1,
            SourceEnum.VERNON,
            CREATED,
            ReceivingTaskType.ONLINE_RECEIVING);

        ReceivingTask result = receivingTaskRepository.save(receivingTasks.getFirst());

        UpdateReceivingTaskItem updateReceivingTaskItem = UpdateReceivingTaskItem.builder()
            .receivingTaskItemId(result.getReceivingTaskItems().getFirst().getId())
            .expectQty(0)
            .build();
        UpdateReceivingTaskCommand command = UpdateReceivingTaskCommand.builder().receivingTaskId(result.getId())
            .receivingTaskItems(List.of(updateReceivingTaskItem))
            .build();
        ReceivingTaskDto receivingTaskDto = receivingTaskResourceApi.updateTask(command);

        assertNotNull(receivingTaskDto);
        assertEquals(0, receivingTaskDto.getReceivingTaskItems().getFirst().getExpectQty());
    }

}