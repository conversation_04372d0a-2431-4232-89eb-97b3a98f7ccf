package com.mercaso.wms.infrastructure.schedule;

import static com.mercaso.wms.utils.MockDataUtils.buildBatch;
import static com.mercaso.wms.utils.MockDataUtils.buildWarehouseDto;
import static org.mockito.Mockito.*;

import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.wms.application.dto.WarehouseDto;
import com.mercaso.wms.application.queryservice.WarehouseQueryService;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.batch.Batch;
import com.mercaso.wms.domain.batch.BatchRepository;
import com.mercaso.wms.domain.warehouse.enums.WarehouseType;
import com.mercaso.wms.infrastructure.config.PgAdvisoryLock;
import com.mercaso.wms.infrastructure.external.finale.FinalePurchaseOrderService;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Collections;
import java.util.List;

class FinalePoCreationSchedulerTest {

    @Mock
    private BatchRepository batchRepository;

    @Mock
    private PgAdvisoryLock pgAdvisoryLock;

    @Mock
    private FeatureFlagsManager featureFlagsManager;

    @Mock
    private WarehouseQueryService warehouseQueryService;

    @Mock
    private FinalePurchaseOrderService finalePurchaseOrderService;

    @InjectMocks
    private FinalePoCreationScheduler scheduler;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void createPurchaseOrders_Does_Nothing_When_No_Lock() {
        when(pgAdvisoryLock.tryLockWithSessionLevel(anyInt())).thenReturn(false);

        scheduler.createPurchaseOrders();

        verifyNoInteractions(batchRepository, warehouseQueryService, finalePurchaseOrderService);
    }

    @Test
    void createPurchaseOrders_Does_Nothing_When_No_Batches_Found() {
        when(pgAdvisoryLock.tryLockWithSessionLevel(anyInt())).thenReturn(true);
        when(batchRepository.findByTag(anyString())).thenReturn(Collections.emptyList());

        scheduler.createPurchaseOrders();

        verify(batchRepository, times(1)).findByTag(anyString());
        verifyNoInteractions(warehouseQueryService, finalePurchaseOrderService);
    }

    @Test
    void createPurchaseOrders_Processes_Batches_For_Warehouses() {
        UUID batchId = UUID.randomUUID();
        UUID warehouseId = UUID.randomUUID();
        SourceEnum sourceEnum = SourceEnum.COSTCO;

        Batch batch = buildBatch(batchId);
        batch.setFinaleEntities(JsonNodeFactory.instance.arrayNode());
        WarehouseDto warehouse = buildWarehouseDto(warehouseId, sourceEnum.name(), WarehouseType.EXTERNAL);

        when(pgAdvisoryLock.tryLockWithSessionLevel(anyInt())).thenReturn(true);
        when(batchRepository.findByTag(anyString())).thenReturn(List.of(batch));
        when(warehouseQueryService.findByType(WarehouseType.EXTERNAL)).thenReturn(List.of(warehouse));
        when(featureFlagsManager.isFeatureOn(anyString())).thenReturn(true);

        scheduler.createPurchaseOrders();

        verify(finalePurchaseOrderService, times(1)).createPurchaseOrder(any(SourceEnum.class), eq(batch));
    }

}