package com.mercaso.wms.application.service;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.businessevents.dispatcher.BusinessEventDispatcher;
import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.wms.application.command.inventorystock.CreateInventoryStockCommand;
import com.mercaso.wms.application.dto.inventory.InventoryStockDto;
import com.mercaso.wms.application.mapper.inventorystock.InventoryStockDtoApplicationMapper;
import com.mercaso.wms.domain.inventorystock.InventoryStock;
import com.mercaso.wms.domain.inventorystock.InventoryStockRepository;
import com.mercaso.wms.domain.inventorystockhistory.InventoryStockHistory;
import com.mercaso.wms.domain.inventorystockhistory.InventoryStockHistoryRepository;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.location.LocationRepository;
import com.mercaso.wms.domain.warehouse.Warehouse;
import com.mercaso.wms.domain.warehouse.WarehouseRepository;
import com.mercaso.wms.domain.warehouse.enums.WarehouseType;
import com.mercaso.wms.infrastructure.cache.LocationCache;
import com.mercaso.wms.infrastructure.config.PgAdvisoryLock;
import com.mercaso.wms.infrastructure.contant.FeatureFlagKeys;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import com.mercaso.wms.infrastructure.external.finale.FinaleProductService;
import com.mercaso.wms.infrastructure.external.finale.dto.FinaleAvailableStockDto;
import com.mercaso.wms.infrastructure.external.finale.dto.FinaleAvailableStockItemsOnHandDto;
import com.mercaso.wms.infrastructure.external.ims.ImsAdaptor;
import java.util.Collections;
import java.util.Map;
import java.util.UUID;
import org.apache.commons.compress.utils.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class InventoryStockApplicationServiceTest {

    @Mock
    private InventoryStockRepository inventoryStockRepository;
    @Mock
    private WarehouseRepository warehouseRepository;
    @Mock
    private LocationRepository locationRepository;
    @Mock
    private BusinessEventDispatcher businessEventDispatcher;
    @Mock
    private InventoryStockHistoryRepository inventoryStockHistoryRepository;
    @Mock
    private InventoryStockDtoApplicationMapper inventoryStockDtoApplicationMapper;
    @Mock
    private ImsAdaptor imsAdaptor;
    @Mock
    private PgAdvisoryLock pgAdvisoryLock;
    @Mock
    private FinaleProductService finaleProductService;
    @Mock
    private LocationCache locationCache;
    @Mock
    private FeatureFlagsManager featureFlagsManager;

    @InjectMocks
    private InventoryStockApplicationService inventoryStockApplicationService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        when(featureFlagsManager.isFeatureOn(FeatureFlagKeys.SYNC_INVENTORY_FROM_FINALE)).thenReturn(true);
    }

    @Test
    void when_create_inventory_stock_then_successfully() {
        CreateInventoryStockCommand command = CreateInventoryStockCommand.builder()
            .warehouseId(UUID.randomUUID())
            .locationId(UUID.randomUUID())
            .build();

        Warehouse warehouse = Warehouse.builder().build();
        Location location = Location.builder().build();
        InventoryStock inventoryStock = InventoryStock.builder().build();
        InventoryStockDto inventoryStockDto = new InventoryStockDto();

        when(warehouseRepository.findById(command.getWarehouseId())).thenReturn(warehouse);
        when(locationRepository.findById(command.getLocationId())).thenReturn(location);
        when(inventoryStockRepository.save(any(InventoryStock.class))).thenReturn(inventoryStock);
        when(inventoryStockDtoApplicationMapper.domainToDto(inventoryStock)).thenReturn(inventoryStockDto);

        InventoryStockDto result = inventoryStockApplicationService.create(command);

        assertNotNull(result);
        verify(businessEventDispatcher).dispatch(any());
        verify(inventoryStockHistoryRepository).save(any(InventoryStockHistory.class));
    }

    @Test
    void when_create_inventory_stock_but_warehouse_not_found_then_throw_exception() {
        CreateInventoryStockCommand command = CreateInventoryStockCommand.builder().warehouseId(UUID.randomUUID()).build();

        when(warehouseRepository.findById(command.getWarehouseId())).thenReturn(null);

        assertThrows(WmsBusinessException.class, () -> inventoryStockApplicationService.create(command));
    }

    @Test
    void when_create_inventory_stock_but_location_not_found_then_throw_exception() {
        CreateInventoryStockCommand command = CreateInventoryStockCommand.builder()
            .warehouseId(UUID.randomUUID())
            .locationId(UUID.randomUUID())
            .build();

        when(warehouseRepository.findById(command.getWarehouseId())).thenReturn(Warehouse.builder().build());
        when(locationRepository.findById(command.getLocationId())).thenReturn(null);

        assertThrows(WmsBusinessException.class, () -> inventoryStockApplicationService.create(command));
    }

    @Test
    void when_available_inventory_stock_but_not_found_then_throw_exception() {
        UUID inventoryStockId = UUID.randomUUID();

        when(inventoryStockRepository.findById(inventoryStockId)).thenReturn(null);

        assertThrows(WmsBusinessException.class, () -> inventoryStockApplicationService.available(inventoryStockId));
    }

    @Test
    void when_unavailable_inventory_stock_but_not_found_then_throw_exception() {
        UUID inventoryStockId = UUID.randomUUID();

        when(inventoryStockRepository.findById(inventoryStockId)).thenReturn(null);

        assertThrows(WmsBusinessException.class, () -> inventoryStockApplicationService.unavailable(inventoryStockId));
    }

    @Test
    void when_sync_inventory_stock_but_warehouse_is_null() {
        when(warehouseRepository.findByType(WarehouseType.INTERNAL)).thenReturn(Lists.newArrayList());

        inventoryStockApplicationService.syncInventoryStocks();

        verify(finaleProductService, never()).getAvailableStock(1000);
        verify(imsAdaptor, times(0)).getItemsBySkus(any());
    }

    @Test
    void syncInventoryStocks_WhenLockNotAcquired_ShouldReturn() {
        // Given
        when(pgAdvisoryLock.tryLockWithSessionLevel(anyInt())).thenReturn(false);

        // When
        inventoryStockApplicationService.syncInventoryStocks();

        // Then
        verify(warehouseRepository, never()).findByType(any());
    }

    @Test
    void syncInventoryStocks_HappyPath() {
        // Given
        when(pgAdvisoryLock.tryLockWithSessionLevel(anyInt())).thenReturn(true);

        Warehouse warehouse = Warehouse.builder()
            .id(UUID.randomUUID())
            .type(WarehouseType.INTERNAL)
            .build();
        when(warehouseRepository.findByName(anyString()))
            .thenReturn(warehouse);

        Location location = Location.builder()
            .id(UUID.randomUUID())
            .name("TEST-LOC")
            .build();
        Map<UUID, Location> locationMap = Map.of(location.getId(), location);
        when(locationCache.getLocationMap()).thenReturn(locationMap);

        InventoryStock existingStock = InventoryStock.builder()
            .id(UUID.randomUUID())
            .warehouse(warehouse)
            .location(location)
            .build();
        when(inventoryStockRepository.findAll())
            .thenReturn(Collections.singletonList(existingStock));

        FinaleAvailableStockDto availableStock = new FinaleAvailableStockDto();
        availableStock.setSku("TEST-SKU");
        FinaleAvailableStockItemsOnHandDto itemOnHand = new FinaleAvailableStockItemsOnHandDto();
        FinaleAvailableStockItemsOnHandDto.Location subLocation = new FinaleAvailableStockItemsOnHandDto.Location();
        subLocation.setName("TEST-LOC");
        itemOnHand.setSubLocation(subLocation);
        itemOnHand.setQuantityOnHand(10L);
        itemOnHand.setPacking("cs 5/1");
        availableStock.setStockItemsOnHand(Collections.singletonList(itemOnHand));

        when(finaleProductService.getAvailableStock(anyInt()))
            .thenReturn(Collections.singletonList(availableStock));

        when(inventoryStockRepository.findBy(warehouse.getId(), "TEST-SKU", location.getId()))
            .thenReturn(existingStock);

        // When
        inventoryStockApplicationService.syncInventoryStocks();

        // Then
        verify(inventoryStockRepository, times(1)).saveAll(any());
    }

    @Test
    void create_ShouldCreateInventoryStock() {
        // Given
        UUID warehouseId = UUID.randomUUID();
        UUID locationId = UUID.randomUUID();
        CreateInventoryStockCommand command = CreateInventoryStockCommand.builder()
            .warehouseId(warehouseId)
            .locationId(locationId)
            .build();

        Warehouse warehouse = Warehouse.builder()
            .id(warehouseId)
            .build();
        when(warehouseRepository.findById(warehouseId)).thenReturn(warehouse);

        Location location = Location.builder()
            .id(locationId)
            .build();
        when(locationRepository.findById(locationId)).thenReturn(location);

        InventoryStock inventoryStock = InventoryStock.builder()
            .id(UUID.randomUUID())
            .build();
        when(inventoryStockRepository.save(any())).thenReturn(inventoryStock);

        // When
        InventoryStockDto result = inventoryStockApplicationService.create(command);

        // Then
        assertNotNull(result);
        verify(businessEventDispatcher).dispatch(any());
        verify(inventoryStockHistoryRepository).save(any());
    }

    @Test
    void syncInventoryStocks_WhenFinaleReturnsEmptyList_ShouldSkipSyncAndPreserveExistingData() {
        // Given
        when(pgAdvisoryLock.tryLockWithSessionLevel(anyInt())).thenReturn(true);

        InventoryStock existingStock1 = InventoryStock.builder()
            .id(UUID.randomUUID())
            .build();
        InventoryStock existingStock2 = InventoryStock.builder()
            .id(UUID.randomUUID())
            .build();
        when(inventoryStockRepository.findAll())
            .thenReturn(java.util.Arrays.asList(existingStock1, existingStock2));

        Map<UUID, Location> locationMap = new java.util.HashMap<>();
        when(locationCache.getLocationMap()).thenReturn(locationMap);

        when(finaleProductService.getAvailableStock(anyInt()))
            .thenReturn(Collections.emptyList());

        // When
        inventoryStockApplicationService.syncInventoryStocks();

        // Then
        // Verify that no stocks were deleted (existingStocks should remain unchanged)
        verify(inventoryStockRepository, never()).saveAll(any());
        // Verify that no new stocks were created
        verify(imsAdaptor, never()).getItemsBySkus(any());
    }
}