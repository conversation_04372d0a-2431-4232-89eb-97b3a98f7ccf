package com.mercaso.wms.application.service.pickingtask;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.wms.application.queryservice.BatchItemQueryService;
import com.mercaso.wms.application.service.PickingTaskApplicationService;
import com.mercaso.wms.batch.config.PickingTaskAssignmentConfig;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.batchitem.BatchItem;
import com.mercaso.wms.domain.batchitem.BatchItemRepository;
import com.mercaso.wms.domain.location.LocationRepository;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.PickingTaskRepository;
import com.mercaso.wms.infrastructure.cache.LocationCache;
import static com.mercaso.wms.utils.MockDataUtils.buildBatchItems;
import static com.mercaso.wms.utils.MockDataUtils.buildPickingTask;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.Test;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class CreateBatchLevelPickingTaskServiceTest {

    private final BatchItemRepository batchItemRepository = mock(BatchItemRepository.class);
    private final BatchItemQueryService batchItemQueryService = mock(BatchItemQueryService.class);
    private final PickingTaskRepository pickingTaskRepository = mock(PickingTaskRepository.class);
    private final LocationRepository locationRepository = mock(LocationRepository.class);
    private final PickingTaskAssignmentConfig pickingTaskAssignmentConfig = mock(PickingTaskAssignmentConfig.class);
    private final PickingTaskApplicationService pickingTaskApplicationService = mock(PickingTaskApplicationService.class);
    private final LocationCache locationCache = mock(LocationCache.class);
    private final FeatureFlagsManager featureFlagsManager = mock(FeatureFlagsManager.class);

    private final CreateBatchLevelPickingTaskService createBatchLevelPickingTask = new CreateBatchLevelPickingTaskService(
        batchItemRepository,
        batchItemQueryService,
        pickingTaskRepository,
        locationRepository,
        pickingTaskAssignmentConfig,
        pickingTaskApplicationService,
        locationCache);


    @Test
    void when_batch_created_then_generate_small_order_picking_task() {
        List<BatchItem> batchItems = buildBatchItems(UUID.randomUUID(), 10);

        when(batchItemQueryService.findSmallBeverageBatchItemsByBatchId(any(), anyString())).thenReturn(batchItems);
        when(pickingTaskRepository.saveAll(any())).thenReturn(buildPickingTask(batchItems.getFirst().getBatchId(), 5));
        List<PickingTask> pickingTasks = createBatchLevelPickingTask.createPickingTask(batchItems.getFirst()
            .getBatchId());

        assertEquals(5, pickingTasks.size());
    }

    @Test
    void when_batch_created_then_generate_picking_task_with_downey_items() {
        List<BatchItem> batchItems = buildBatchItems(UUID.randomUUID(), 10);

        when(batchItemQueryService.findBigBeverageBatchItemsByBatchId(any(), any())).thenReturn(List.of());
        when(batchItemQueryService.findBy(any(), anyString())).thenReturn(batchItems);
        List<PickingTask> builtPickingTask = buildPickingTask(batchItems.getFirst().getBatchId(), 10);
        builtPickingTask.forEach(pickingTask -> pickingTask.setSource(SourceEnum.DOWNEY));
        when(pickingTaskRepository.saveAll(any())).thenReturn(builtPickingTask);

        List<PickingTask> pickingTasks = createBatchLevelPickingTask.createPickingTask(batchItems.getFirst()
            .getBatchId());

        assertEquals(10, pickingTasks.size());
        pickingTasks.forEach(pickingTask -> {
            assertEquals(2, pickingTask.getPickingTaskItems().size());
            assertEquals(batchItems.getFirst().getBatchId(), pickingTask.getBatchId());
            assertEquals(SourceEnum.DOWNEY, pickingTask.getSource());
        });
    }

    @Test
    void when_batch_created_then_generate_picking_task_with_Costco_items() {
        List<BatchItem> batchItems = buildBatchItems(UUID.randomUUID(), 10);
        batchItems.forEach(batchItem -> batchItem.setSource(SourceEnum.COSTCO.name()));
        when(batchItemQueryService.findBigBeverageBatchItemsByBatchId(any(), any())).thenReturn(List.of());
        when(batchItemQueryService.findBy(any(), anyString())).thenReturn(List.of());
        List<PickingTask> builtPickingTask = buildPickingTask(batchItems.getFirst().getBatchId(), 10);
        builtPickingTask.forEach(pickingTask -> pickingTask.setSource(SourceEnum.COSTCO));
        when(batchItemQueryService.findBy(batchItems.getFirst().getBatchId(), SourceEnum.COSTCO.name())).thenReturn(batchItems);
        when(pickingTaskRepository.saveAll(any())).thenReturn(builtPickingTask);

        List<PickingTask> pickingTasks = createBatchLevelPickingTask.createPickingTask(batchItems.getFirst()
            .getBatchId());

        assertEquals(10, pickingTasks.size());
        pickingTasks.forEach(pickingTask -> {
            assertEquals(2, pickingTask.getPickingTaskItems().size());
            assertEquals(batchItems.getFirst().getBatchId(), pickingTask.getBatchId());
            assertEquals(SourceEnum.COSTCO, pickingTask.getSource());
        });
    }

    @Test
    void when_batch_created_then_generate_picking_task_with_jetro_items() {
        List<BatchItem> batchItems = buildBatchItems(UUID.randomUUID(), 10);
        for (BatchItem batchItem : batchItems) {
            batchItem.setSource(SourceEnum.JETRO.name());
        }

        when(batchItemQueryService.findBigBeverageBatchItemsByBatchId(any(), any())).thenReturn(List.of());
        when(batchItemQueryService.findBy(any(), anyString())).thenReturn(batchItems);
        List<PickingTask> builtPickingTask = buildPickingTask(batchItems.getFirst().getBatchId(), 10);
        builtPickingTask.forEach(pickingTask -> pickingTask.setSource(SourceEnum.JETRO));
        when(pickingTaskRepository.saveAll(any())).thenReturn(builtPickingTask);

        List<PickingTask> pickingTasks = createBatchLevelPickingTask.createPickingTask(batchItems.getFirst()
            .getBatchId());

        assertEquals(10, pickingTasks.size());
        pickingTasks.forEach(pickingTask -> {
            assertEquals(2, pickingTask.getPickingTaskItems().size());
            assertEquals(batchItems.getFirst().getBatchId(), pickingTask.getBatchId());
            assertEquals(SourceEnum.JETRO, pickingTask.getSource());
        });
    }

    @Test
    void createMfcBatchPickingTask_Success() {
        // Given
        UUID batchId = UUID.randomUUID();

        BatchItem item1 = BatchItem.builder()
            .department("DEPARTMENT_1")
            .locationName("A01-01")
            .expectQty(10)
            .skuNumber("SKU_1")
            .source(SourceEnum.MFC.name())
            .build();
        BatchItem item2 = BatchItem.builder()
            .department("DEPARTMENT_1")
            .locationName("A01-02")
            .expectQty(10)
            .skuNumber("SKU_2")
            .source(SourceEnum.MFC.name())
            .build();
        List<BatchItem> mfcBatchItems = Arrays.asList(item1, item2);

        when(featureFlagsManager.isFeatureOn(any())).thenReturn(true);
        when(batchItemQueryService.findBy(batchId, SourceEnum.MFC.name())).thenReturn(mfcBatchItems);
        when(locationCache.getLocationMap()).thenReturn(new HashMap<>());
        when(pickingTaskRepository.saveAll(anyList())).thenReturn(List.of(PickingTask.builder().build()));

        // When
        List<PickingTask> pickingTasks = createBatchLevelPickingTask.createPickingTask(batchId);

        // Then
        verify(batchItemQueryService).findBy(batchId, SourceEnum.MFC.name());
        assertEquals(1, pickingTasks.size());
    }

    @Test
    void createMfcBatchPickingTask_EmptyBatchItems_NoTasksCreated() {
        // Given
        UUID batchId = UUID.randomUUID();

        when(featureFlagsManager.isFeatureOn(any())).thenReturn(true);
        when(batchItemQueryService.findBy(batchId, SourceEnum.MFC.name())).thenReturn(Collections.emptyList());

        // When
        List<PickingTask> pickingTasks = createBatchLevelPickingTask.createPickingTask(batchId);

        // Then
        verify(batchItemQueryService).findBy(batchId, SourceEnum.MFC.name());
        assertTrue(pickingTasks.isEmpty());
    }

}