package com.mercaso.wms.utils;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.mercaso.wms.application.command.receivingtask.UpdateReceivingTaskCommand;
import com.mercaso.wms.application.dto.Result;
import com.mercaso.wms.application.dto.receivingtask.ReceivingTaskDto;
import com.mercaso.wms.application.dto.receivingtask.ReceivingTaskItemDto;
import com.mercaso.wms.application.query.SortType;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.receivingtask.enums.ReceivingTaskStatus;
import com.mercaso.wms.domain.receivingtask.enums.ReceivingTaskType;
import com.mercaso.wms.infrastructure.utils.SerializationUtils;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
public class ReceivingTaskResourceApi extends IntegrationTestRestUtil {

    private static final String SEARCH_PICKING_TASKS_URL = "/search/receiving-tasks";

    public static final String FIND_BY_ID_URL = "/query/receiving-tasks/%s";

    public static final String SCAN_RECEIVE_ITEM_URL = "/receiving-tasks/scan-receive/%s";

    public static final String RECEIVE_TASK_URL = "/receiving-tasks/%s/receive";

    public static final String UPDATE_TASK_URL = "/receiving-tasks";

    public ReceivingTaskResourceApi(Environment environment) {
        super(environment);
    }

    public Result<ReceivingTaskDto> search(
        List<String> numbers,
        SourceEnum source,
        ReceivingTaskStatus[] statuses,
        LocalDate deliveryDate,
        ReceivingTaskType type,
        List<String> orderNumbers,
        String breakdownName,
        List<SortType> sortTypes) throws Exception {
        StringBuilder url = new StringBuilder(SEARCH_PICKING_TASKS_URL + "?page=1&pageSize=20");
        if (numbers != null) {
            url.append("&numbers=").append(String.join(",", numbers));
        }
        if (source != null) {
            url.append("&source=").append(source);
        }
        if (statuses != null) {
            url.append("&statuses=").append(String.join(",", Arrays.stream(statuses).map(Enum::name).toArray(String[]::new)));
        }
        if (deliveryDate != null) {
            url.append("&deliveryDate=").append(deliveryDate);
        }
        if (type != null) {
            url.append("&type=").append(type);
        }
        if (orderNumbers != null) {
            url.append("&orderNumbers=").append(String.join(",", orderNumbers));
        }
        if (breakdownName != null) {
            url.append("&breakdownName=").append(breakdownName);
        }
        if (!CollectionUtils.isEmpty(sortTypes)) {
            List<String> sortTypesStr = sortTypes.stream().map(Enum::toString).toList();
            url.append("&sortTypes=").append(String.join(",", sortTypesStr));
        }
        String body = getEntity(url.toString(), String.class).getBody();
        return SerializationUtils.readValue(body, new TypeReference<Result<ReceivingTaskDto>>() {
        });
    }

    public ReceivingTaskDto getReceivingTask(UUID id) {
        return getEntity(String.format(FIND_BY_ID_URL, id), ReceivingTaskDto.class).getBody();
    }

    public ReceivingTaskItemDto scanReceiveItem(UUID id) throws JsonProcessingException {
        return updateEntity(String.format(SCAN_RECEIVE_ITEM_URL, id), null, ReceivingTaskItemDto.class);
    }

    public ReceivingTaskDto receiveTask(UUID id) throws JsonProcessingException {
        return updateEntity(String.format(RECEIVE_TASK_URL, id), null, ReceivingTaskDto.class);
    }

    public ReceivingTaskDto updateTask(UpdateReceivingTaskCommand command) throws JsonProcessingException {
        return updateEntity(UPDATE_TASK_URL, command, ReceivingTaskDto.class);
    }
}
