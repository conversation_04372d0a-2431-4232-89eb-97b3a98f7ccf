package com.mercaso.wms.delivery.infrastructure.repository.deliveryorder.jpa;

import com.mercaso.wms.delivery.application.dto.deliveryorder.AddressDto;
import com.mercaso.wms.delivery.application.dto.view.SearchDeliveryOrderView;
import com.mercaso.wms.delivery.application.query.DeliveryOrderQuery;
import com.mercaso.wms.infrastructure.utils.SerializationUtils;
import java.io.IOException;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class DeliveryOrderJdbcTemplate {

    private final NamedParameterJdbcTemplate jdbcTemplate;

    public Page<SearchDeliveryOrderView> search(DeliveryOrderQuery query, Pageable pageable) {
        MapSqlParameterSource params = new MapSqlParameterSource();

        DeliveryOrderSearchSqlBuilder sqlBuilder = DeliveryOrderSearchSqlBuilder.builder()
            .query(query)
            .pageable(pageable)
            .params(params)
            .build();

        String selectSql = sqlBuilder.buildSelectSql();
        String countSql = sqlBuilder.buildCountSql();

        Long total = jdbcTemplate.queryForObject(countSql, params, Long.class);

        List<SearchDeliveryOrderView> content = jdbcTemplate.query(selectSql, params,
            (rs, rowNum) -> convert(rs));

        return new PageImpl<>(content, pageable, total != null ? total : 0);
    }

    private SearchDeliveryOrderView convert(ResultSet rs) throws SQLException {
        SearchDeliveryOrderView view = new SearchDeliveryOrderView();
        view.setId(rs.getObject("id", UUID.class));
        view.setOrderNumber(rs.getString("order_number"));
        view.setStatus(rs.getString("status"));
        view.setDeliveryDate(rs.getString("delivery_date"));
        view.setDeliveryTimeWindow(rs.getString("delivery_time_window"));
        view.setPaymentType(rs.getString("payment_type"));
        view.setPaymentStatus(rs.getString("payment_status"));
        view.setFulfillmentStatus(rs.getString("fulfillment_status"));
        view.setCustomerNotes(rs.getString("customer_notes"));
        view.setNotes(rs.getString("notes"));
        view.setSequence(rs.getInt("sequence"));
        view.setCurrentQty(rs.getBigDecimal("currentQty"));
        view.setDeliveredQty(rs.getBigDecimal("deliveredQty"));
        view.setRescheduleType(rs.getString("reschedule_type"));
        view.setPlanArriveAt(rs.getTimestamp("plan_arrive_at") != null ? rs.getTimestamp("plan_arrive_at").toInstant() : null);
        view.setPlanDeliveryAt(
            rs.getTimestamp("plan_delivery_at") != null ? rs.getTimestamp("plan_delivery_at").toInstant() : null);
        view.setArrivedAt(rs.getTimestamp("arrived_at") != null ? rs.getTimestamp("arrived_at").toInstant() : null);
        view.setUnloadedAt(
            rs.getTimestamp("unloaded_at") != null ? rs.getTimestamp("unloaded_at").toInstant() : null);
        view.setDeliveredAt(rs.getTimestamp("delivered_at") != null ? rs.getTimestamp("delivered_at").toInstant() : null);
        // Delivery task information
        view.setDeliveryTaskId(rs.getObject("delivery_task_id", UUID.class));
        view.setDeliveryTaskNumber(rs.getString("delivery_task_number"));
        view.setDriverUserName(rs.getString("driver_user_name"));
        view.setTruckNumber(rs.getString("truck_number"));
        String address = rs.getString("address");
        if (address != null) {
            try {
                view.setAddress(SerializationUtils.deserialize(address, AddressDto.class));
            } catch (IOException e) {
                log.warn("[DeliveryOrderJdbcTemplate] Failed to deserialize address", e);
            }
        }
        view.setOriginalTotalPrice(rs.getBigDecimal("original_total_price"));
        view.setTotalPrice(rs.getBigDecimal("total_price"));
        return view;
    }
}
