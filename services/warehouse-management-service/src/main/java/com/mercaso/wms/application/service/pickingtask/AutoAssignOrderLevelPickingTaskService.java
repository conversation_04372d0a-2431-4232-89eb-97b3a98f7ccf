package com.mercaso.wms.application.service.pickingtask;

import com.mercaso.wms.application.command.pickingtask.AssignPickingTaskCommand;
import com.mercaso.wms.application.service.PickingTaskApplicationService;
import com.mercaso.wms.batch.config.PickingTaskAssignmentProperties;
import com.mercaso.wms.batch.config.PickingTaskAssignmentProperties.Picker;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItem;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@AllArgsConstructor
public class AutoAssignOrderLevelPickingTaskService {

    private final PickingTaskApplicationService pickingTaskApplicationService;

    public void assignPickingTask(List<PickingTask> pickingTasks, PickingTaskAssignmentProperties config) {
        if (config == null || Boolean.FALSE.equals(config.getAutoAssignment())) {
            return;
        }

        List<Picker> availablePickers = new ArrayList<>(config.getPickers());
        if (availablePickers.isEmpty()) {
            log.info("No available pickers configured. Skipping assignment.");
            return;
        }

        pickingTasks.sort(Comparator.comparing(pickingTask -> pickingTask.getPickingTaskItems()
            .getFirst()
            .getBreakdownName()));
        List<PickingTask> sortedTasks = pickingTasks.reversed();

        Map<UUID, Integer> pickerCurrentQty = initializePickerQuantities(availablePickers);
        int maxQty = config.getTargetQty() + config.getTolerance();

        assignTasksToPickers(sortedTasks, availablePickers, pickerCurrentQty, maxQty);
    }

    private Map<UUID, Integer> initializePickerQuantities(List<Picker> pickers) {
        Map<UUID, Integer> pickerCurrentQty = new HashMap<>();
        pickers.forEach(picker -> pickerCurrentQty.put(picker.getUserId(), 0));
        return pickerCurrentQty;
    }

    private void assignTasksToPickers(List<PickingTask> sortedTasks,
        List<Picker> availablePickers,
        Map<UUID, Integer> pickerCurrentQty,
        int maxQty) {
        List<Picker> activePickers = new ArrayList<>(availablePickers);

        Map<PickingTask, Integer> taskQuantities = new HashMap<>();
        sortedTasks.forEach(task ->
            taskQuantities.put(task, calculateTaskQuantity(task)));

        int currentPickerIndex = 0;

        for (int i = 0; i < sortedTasks.size() && !activePickers.isEmpty(); i++) {
            PickingTask task = sortedTasks.get(i);
            int taskQty = taskQuantities.get(task);

            AssignmentResult result = tryAssignTaskToPicker(task, taskQty, activePickers,
                pickerCurrentQty, currentPickerIndex, maxQty);

            if (!result.isSuccess()) {
                log.info("No available picker found for task with quantity {}. Stopping assignment.", taskQty);
                break;
            }
            currentPickerIndex = result.getNextPickerIndex();
        }
    }

    private int calculateTaskQuantity(PickingTask task) {
        return task.getPickingTaskItems().stream()
            .mapToInt(PickingTaskItem::getExpectQty)
            .sum();
    }

    private AssignmentResult tryAssignTaskToPicker(PickingTask task,
        int taskQty,
        List<Picker> availablePickers,
        Map<UUID, Integer> pickerCurrentQty,
        int currentPickerIndex,
        int maxQty) {
        if (availablePickers.isEmpty()) {
            return new AssignmentResult(false, currentPickerIndex);
        }

        final int pickerCount = availablePickers.size();
        int pickerIndex = currentPickerIndex;

        for (int i = 0; i < pickerCount; i++) {
            Picker currentPicker = availablePickers.get(pickerIndex);
            UUID pickerId = currentPicker.getUserId();
            int currentQty = pickerCurrentQty.get(pickerId);
            int newTotal = currentQty + taskQty;

            if (newTotal <= maxQty) {
                AssignPickingTaskCommand command = AssignPickingTaskCommand.builder()
                    .pickerUserId(pickerId)
                    .pickerUserName(currentPicker.getUserName())
                    .build();

                pickingTaskApplicationService.assignPickingTask(task.getId(), command);
                pickerCurrentQty.put(pickerId, newTotal);

                if (newTotal >= maxQty) {
                    availablePickers.remove(pickerIndex);
                    if (!availablePickers.isEmpty()) {
                        pickerIndex %= availablePickers.size();
                    }
                } else {
                    pickerIndex = (pickerIndex + 1) % pickerCount;
                }
                return new AssignmentResult(true, pickerIndex);
            }
            pickerIndex = (pickerIndex + 1) % pickerCount;
        }
        return new AssignmentResult(false, currentPickerIndex);
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    private static class AssignmentResult {

        boolean success;
        int nextPickerIndex;
    }

}
