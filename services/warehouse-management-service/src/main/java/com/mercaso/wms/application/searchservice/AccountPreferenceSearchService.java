package com.mercaso.wms.application.searchservice;

import com.mercaso.wms.application.dto.view.AccountPreferenceView;
import com.mercaso.wms.application.query.AccountPreferenceQuery;
import com.mercaso.wms.infrastructure.repository.accountpreferences.jpa.AccountPreferenceJdbcTemplate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

@Slf4j
@Validated
@Service
@RequiredArgsConstructor
public class AccountPreferenceSearchService {

    private final AccountPreferenceJdbcTemplate accountPreferenceJdbcTemplate;

    public Page<AccountPreferenceView> search(AccountPreferenceQuery query, Pageable pageable) {
        return accountPreferenceJdbcTemplate.searchBy(query, pageable);
    }
}