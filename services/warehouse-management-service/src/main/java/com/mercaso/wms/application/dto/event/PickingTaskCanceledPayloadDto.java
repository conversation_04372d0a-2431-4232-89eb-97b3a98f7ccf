package com.mercaso.wms.application.dto.event;

import com.mercaso.wms.application.dto.PickingTaskDto;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class PickingTaskCanceledPayloadDto extends BusinessEventPayloadDto<PickingTaskDto> {

    private UUID pickingTaskId;

    private String reason;

    @Builder
    public PickingTaskCanceledPayloadDto(PickingTaskDto data, UUID pickingTaskId, String reason) {
        super(data);
        this.pickingTaskId = pickingTaskId;
        this.reason = reason;
    }

}
