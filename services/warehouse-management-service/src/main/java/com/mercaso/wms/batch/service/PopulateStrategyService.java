package com.mercaso.wms.batch.service;

import com.mercaso.wms.batch.dto.ExcelBatchDto;
import com.mercaso.wms.batch.dto.PopulateCondition;
import com.mercaso.wms.batch.strategy.PopulateBatchTemplateStrategy;
import com.mercaso.wms.batch.strategy.impl.FullBreakDownSmallPopulateStrategy;
import com.mercaso.wms.batch.strategy.impl.FullBreakdownPopulateStrategy;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class PopulateStrategyService {

    private final List<PopulateBatchTemplateStrategy> strategies;

    @Autowired
    public PopulateStrategyService(List<PopulateBatchTemplateStrategy> strategies) {
        this.strategies = strategies.stream()
                .filter(strategy -> !(strategy instanceof FullBreakdownPopulateStrategy) &&
                        !(strategy instanceof FullBreakDownSmallPopulateStrategy))
            .toList();
    }

    public List<ExcelBatchDto> populateBatchTemplate(PopulateCondition populateCondition) {
        List<ExcelBatchDto> result = null;
        for (PopulateBatchTemplateStrategy strategy : strategies) {
            result = strategy.populateBatchTemplate(populateCondition);
            populateCondition.setExcelBatchDtoList(result);
        }
        return result;
    }
}