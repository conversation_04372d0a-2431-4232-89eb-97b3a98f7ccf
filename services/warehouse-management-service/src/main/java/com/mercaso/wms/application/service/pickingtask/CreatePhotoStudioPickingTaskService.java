package com.mercaso.wms.application.service.pickingtask;

import com.mercaso.wms.application.queryservice.BatchItemQueryService;
import com.mercaso.wms.application.service.PickingTaskApplicationService;
import com.mercaso.wms.batch.config.PickingTaskAssignmentConfig;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.batchitem.BatchItem;
import com.mercaso.wms.domain.batchitem.BatchItemRepository;
import com.mercaso.wms.domain.location.LocationRepository;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.PickingTaskRepository;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import com.mercaso.wms.infrastructure.cache.LocationCache;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class CreatePhotoStudioPickingTaskService extends CreatePickingTaskService {

    protected CreatePhotoStudioPickingTaskService(BatchItemRepository batchItemRepository,
        BatchItemQueryService batchItemQueryService,
        PickingTaskRepository pickingTaskRepository,
        LocationRepository locationRepository,
        PickingTaskAssignmentConfig pickingTaskAssignmentConfig,
        PickingTaskApplicationService pickingTaskApplicationService,
        LocationCache locationCache) {
        super(batchItemRepository,
            batchItemQueryService,
            pickingTaskRepository,
            locationRepository,
            pickingTaskAssignmentConfig,
            pickingTaskApplicationService,
            locationCache);
    }

    @Override
    public List<PickingTask> createPickingTask(UUID batchId) {
        List<BatchItem> photoStudioItems = batchItemQueryService.findByBatchIdAndSourceAndLocationName(batchId,
            SourceEnum.MDC.name(),
            "PHOTO-STUDIO");

        if (photoStudioItems.isEmpty()) {
            return Collections.emptyList();
        }
        photoStudioItems.sort(Comparator.comparing(BatchItem::getTitle, Comparator.nullsLast(Comparator.naturalOrder())));
        PickingTask pickingTask = PickingTask.builder()
            .build()
            .createTask(batchId, SourceEnum.MDC, photoStudioItems, locationCache.getLocationMap());
        pickingTask.setType(PickingTaskType.BATCH);
        PickingTask save = pickingTaskRepository.save(pickingTask);
        log.info("Created photo studio picking task: {}", save.getNumber());
        return List.of(save);
    }
}
