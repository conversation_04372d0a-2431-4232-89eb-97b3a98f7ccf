package com.mercaso.wms.delivery.application.search;

import com.mercaso.wms.delivery.application.dto.view.SearchDeliveryTaskView;
import com.mercaso.wms.delivery.application.query.DeliveryTaskQuery;
import com.mercaso.wms.delivery.infrastructure.repository.deliverytask.jpa.DeliveryTaskJdbcTemplate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

@Slf4j
@Validated
@Service
@RequiredArgsConstructor
public class DeliveryTaskSearchService {

    private final DeliveryTaskJdbcTemplate jdbcTemplate;

    public Page<SearchDeliveryTaskView> search(DeliveryTaskQuery query, Pageable pageable) {
        log.debug("Searching delivery tasks with query: {}", query);
        return jdbcTemplate.search(query, pageable);
    }
} 