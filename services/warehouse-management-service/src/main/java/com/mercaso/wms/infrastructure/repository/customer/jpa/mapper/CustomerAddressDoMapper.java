package com.mercaso.wms.infrastructure.repository.customer.jpa.mapper;

import com.mercaso.wms.domain.customeraddress.CustomerAddress;
import com.mercaso.wms.infrastructure.repository.BaseDoMapper;
import com.mercaso.wms.infrastructure.repository.customer.jpa.dataobject.CustomerAddressDo;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface CustomerAddressDoMapper extends BaseDoMapper<CustomerAddressDo, CustomerAddress> {

}
