package com.mercaso.wms.application.dto.event;

import com.mercaso.wms.application.dto.PickingTaskDto;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class PickingTaskStartedPayloadDto extends BusinessEventPayloadDto<PickingTaskDto> {

    private UUID pickingTaskId;

    @Builder
    public PickingTaskStartedPayloadDto(PickingTaskDto data, UUID pickingTaskId) {
        super(data);
        this.pickingTaskId = pickingTaskId;
    }

}
