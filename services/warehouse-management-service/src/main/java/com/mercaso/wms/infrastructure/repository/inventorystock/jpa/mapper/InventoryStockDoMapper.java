package com.mercaso.wms.infrastructure.repository.inventorystock.jpa.mapper;

import com.mercaso.wms.domain.inventorystock.InventoryStock;
import com.mercaso.wms.infrastructure.repository.BaseDoMapper;
import com.mercaso.wms.infrastructure.repository.inventorystock.jpa.dataobject.InventoryStockDo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface InventoryStockDoMapper extends BaseDoMapper<InventoryStockDo, InventoryStock> {

    @Mapping(source = "item.id", target = "itemId")
    @Mapping(source = "item.skuNumber", target = "skuNumber")
    @Mapping(source = "item.title", target = "title")
    InventoryStockDo domainToDo(InventoryStock domain);

    @Mapping(source = "itemId", target = "item.id")
    @Mapping(source = "skuNumber", target = "item.skuNumber")
    @Mapping(source = "title", target = "item.title")
    InventoryStock doToDomain(InventoryStockDo dataObject);
}
