package com.mercaso.wms.application.searchservice;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.mercaso.wms.application.dto.PickedItemsDto;
import com.mercaso.wms.application.dto.PickingTaskDto;
import com.mercaso.wms.application.dto.SearchPickingTaskView;
import com.mercaso.wms.application.dto.shippingorder.ShippingOrderDto;
import com.mercaso.wms.application.dto.shippingorder.ShippingOrderItemDto;
import com.mercaso.wms.application.mapper.pickingtask.PickingTaskDtoApplicationMapper;
import com.mercaso.wms.application.query.PickingTaskQuery;
import com.mercaso.wms.application.queryservice.ShippingOrderQueryService;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.PickingTaskRepository;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItem;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import com.mercaso.wms.infrastructure.repository.pickingtask.jpa.PickingTaskJdbcTemplate;
import com.mercaso.wms.infrastructure.repository.pickingtask.jpa.criteria.PickingTaskSearchCriteria;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

@Slf4j
@Validated
@Service
@RequiredArgsConstructor
public class PickingTaskSearchService {

    private final PickingTaskRepository pickingTaskRepository;
    private final ShippingOrderQueryService shippingOrderQueryService;
    private final PickingTaskDtoApplicationMapper pickingTaskDtoApplicationMapper;
    private final PickingTaskJdbcTemplate pickingTaskJdbcTemplate;

    public Page<PickingTaskDto> searchPickingTasks(PickingTaskQuery pickingTaskQuery, Pageable pageable) {
        PickingTaskSearchCriteria criteria = getPickingTaskSearchCriteria(pickingTaskQuery);
        Page<PickingTask> pickingTaskList = pickingTaskRepository.findPickingTaskList(criteria, pageable);
        Page<PickingTaskDto> page = pickingTaskList.map(pickingTaskDtoApplicationMapper::domainToDto);
        setOrderQtyIfNeeded(pickingTaskList, page);
        return page;
    }

    public Page<SearchPickingTaskView> searchPickingTasksV2(PickingTaskQuery pickingTaskQuery, Pageable pageable) {
        PickingTaskSearchCriteria criteria = getPickingTaskSearchCriteria(pickingTaskQuery);
        Page<SearchPickingTaskView> searchPickingTaskViews = pickingTaskRepository.searchBy(criteria, pageable);
        setOrderQtyIfNeeded(searchPickingTaskViews);
        return searchPickingTaskViews;
    }

    private static PickingTaskSearchCriteria getPickingTaskSearchCriteria(PickingTaskQuery pickingTaskQuery) {
        PickingTaskSearchCriteria criteria = new PickingTaskSearchCriteria();
        BeanUtils.copyProperties(pickingTaskQuery, criteria);
        criteria.setDeliveryDate(
            pickingTaskQuery.getDeliveryDate() == null ? null : pickingTaskQuery.getDeliveryDate().toString());
        return criteria;
    }

    private void setOrderQtyIfNeeded(Page<PickingTask> pickingTaskList, Page<PickingTaskDto> page) {
        try {
            Set<String> orderNumbers = pickingTaskList.getContent().stream()
                .filter(pickingTask -> PickingTaskType.ORDER == pickingTask.getType())
                .flatMap(pickingTask -> pickingTask.getPickingTaskItems().stream().map(PickingTaskItem::getOrderNumber))
                .collect(Collectors.toSet());
            List<ShippingOrderDto> shippingOrderDtos = shippingOrderQueryService.findByOrderNumbers(orderNumbers.stream()
                .toList());
            page.getContent().forEach(pickingTask -> {
                if (PickingTaskType.ORDER == pickingTask.getType()) {
                    shippingOrderDtos.forEach(shippingOrderDto -> {
                        if (pickingTask.getPickingTaskItems().stream()
                            .anyMatch(pickingTaskItem -> pickingTaskItem.getOrderNumber() != null
                                && pickingTaskItem.getOrderNumber().equals(shippingOrderDto.getOrderNumber()))) {
                            pickingTask.setOrderQty(shippingOrderDto.getShippingOrderItems()
                                .stream()
                                .mapToInt(ShippingOrderItemDto::getQty)
                                .sum());
                        }
                    });
                }
            });
        } catch (WmsBusinessException e) {
            log.error("[searchPickingTasks] Failed to set order qty for picking tasks.", e);
        }
    }

    private void setOrderQtyIfNeeded(Page<SearchPickingTaskView> page) {
        try {
            List<SearchPickingTaskView> pickingTaskViews = page.getContent();
            List<String> orderNumbers = pickingTaskViews.stream()
                .filter(pickingTask -> PickingTaskType.ORDER == pickingTask.getType())
                .flatMap(pickingTask -> CollectionUtils.isEmpty(pickingTask.getOrderNumbers()) ? null
                    : pickingTask.getOrderNumbers().stream())
                .distinct()
                .toList();
            List<ShippingOrderDto> shippingOrderDtoList = shippingOrderQueryService.findByOrderNumbers(orderNumbers);
            page.getContent().forEach(searchPickingTaskView -> shippingOrderDtoList.forEach(shippingOrderDto -> {
                if (searchPickingTaskView.getOrderNumbers().contains(shippingOrderDto.getOrderNumber())) {
                    searchPickingTaskView.setOrderQty(shippingOrderDto.getShippingOrderItems()
                        .stream()
                        .mapToInt(ShippingOrderItemDto::getQty)
                        .sum());
                }
            }));
        } catch (WmsBusinessException e) {
            log.error("[searchPickingTasksV2] Failed to set order qty for picking tasks.", e);
        }
    }

    public ByteArrayOutputStream pickedItemsExport(String deliveryDate) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            List<PickedItemsDto> pickedItemsDtos = pickingTaskJdbcTemplate.fetchPickedItemsForDeliveryDate(deliveryDate);
            if (CollectionUtils.isEmpty(pickedItemsDtos)) {
                return null;
            }

            pickedItemsDtos = pickedItemsDtos.stream()
                .collect(Collectors.groupingBy(
                    dto -> dto.getOrderNumber() + dto.getLine() + dto.getItemNumber(),
                    Collectors.reducing((firstDto, secondDto) -> {
                        firstDto.setPickedQty(Optional.ofNullable(firstDto.getPickedQty()).orElse(0)
                            + Optional.ofNullable(secondDto.getPickedQty()).orElse(0));
                        firstDto.setRequestedQty(Optional.ofNullable(firstDto.getRequestedQty()).orElse(0)
                            + Optional.ofNullable(secondDto.getRequestedQty()).orElse(0));
                        return firstDto;
                    })))
                .values()
                .stream()
                .flatMap(Optional::stream)
                .collect(Collectors.toList());

            return writeTemplate(outputStream, pickedItemsDtos);
        } catch (Exception e) {
            log.error("Failed to export picked items.", e);
            return null;
        }
    }

    public ByteArrayOutputStream writeTemplate(ByteArrayOutputStream outputStream,
        List<PickedItemsDto> pickedItemsDtos) {
        Resource resource = new ClassPathResource("template/Picked_Items_List_Template.xlsx");
        try (ExcelWriter excelWriter = EasyExcelFactory.write(outputStream)
            .withTemplate(resource.getInputStream())
            .build()) {
            excelWriter.fill(pickedItemsDtos, EasyExcelFactory.writerSheet().sheetName("Picked Items").build());
        } catch (IOException e) {
            throw new WmsBusinessException("Failed to write picked items.", e);
        }
        return outputStream;
    }
}