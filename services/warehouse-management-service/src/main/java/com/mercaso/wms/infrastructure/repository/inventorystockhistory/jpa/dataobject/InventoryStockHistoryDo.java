package com.mercaso.wms.infrastructure.repository.inventorystockhistory.jpa.dataobject;

import com.mercaso.wms.domain.businessevent.EntityEnums;
import com.mercaso.wms.domain.inventorystockhistory.enums.TransactionEvent;
import com.mercaso.wms.infrastructure.repository.BaseDo;
import com.mercaso.wms.infrastructure.repository.location.jpa.dataobject.LocationDo;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

@Entity
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "inventory_stock_history")
@SQLDelete(sql = "update inventory_stock_history set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
public class InventoryStockHistoryDo extends BaseDo {

    @Column(name = "from_stock_id")
    private UUID fromStockId;

    @Column(name = "to_stock_id")
    private UUID toStockId;

    @Column(name = "sku_number", length = 20)
    private String skuNumber;

    @Column(name = "title")
    private String title;

    @Column(name = "item_id")
    private UUID itemId;

    @JoinColumn(name = "from_location_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private LocationDo fromLocation;

    @JoinColumn(name = "to_location_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private LocationDo toLocation;

    @Column(name = "entity_id")
    private UUID entityId;

    @Column(name = "entity_name")
    @Enumerated(EnumType.STRING)
    private EntityEnums entityName;

    @Column(name = "lot_number", length = 50)
    private String lotNumber;

    @Column(name = "production_date")
    private LocalDate productionDate;

    @Column(name = "expiration_date")
    private LocalDate expirationDate;

    @Column(name = "before_qty", nullable = false, precision = 10, scale = 2)
    private BigDecimal beforeQty = BigDecimal.ZERO;

    @Column(name = "after_qty", nullable = false, precision = 10, scale = 2)
    private BigDecimal afterQty = BigDecimal.ZERO;

    @Column(name = "change_qty", nullable = false, precision = 10, scale = 2)
    private BigDecimal changeQty = BigDecimal.ZERO;

    @Column(name = "vendor_id")
    private UUID vendorId;

    @Column(name = "transaction_event", length = 20)
    @Enumerated(EnumType.STRING)
    private TransactionEvent transactionEvent;

    @Column(name = "reason")
    private String reason;

}
