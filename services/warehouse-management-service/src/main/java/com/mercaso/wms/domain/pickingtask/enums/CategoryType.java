package com.mercaso.wms.domain.pickingtask.enums;

import lombok.Getter;

@Getter
public enum CategoryType {
    MFC_BEVERAGE_CLEANING("MFC Beverage & Cleaning", 40),
    MFC_CANDY_OTHER("MFC Candy & Other", 100),
    B<PERSON><PERSON>RA<PERSON>("Beverage", 40),
    CANDY_AND_SNACKS("Candy & Snacks", 100),
    TOBACCO("Tobacco", 100),
    GROCERY("Grocery", 40),
    REFRIGERATED("Refrigerated", 40),
    HOUSEHOLD_AND_KITCHEN("Household & Kitchen", 40),
    RESTAURANT_SUPPLIES_AND_INGREDIENTS("Restaurant Supplies & Ingredients", 40),
    HEALTH_AND_BEAUTY("Health & Beauty", 100),
    BABY("Baby", 40),
    CLEANING_AND_LAUNDRY("Cleaning & Laundry", 40),
    STORE_SUPPLIES("Store Supplies", 40),
    AUTO_AND_ELECTRONICS("Auto & Electronics", 40),
    OFFICE_AND_SCHOOL_SUPPLIES("Office & School Supplies", 100),
    PET("Pet", 40),
    HARDWARE_AND_GARDENING("Hardware & Gardening", 40),
    PRODUCE("Produce", 40),
    PARTY_AND_GIFT_SUPPLIES("Party & Gift Supplies", 100),
    OTHER("Other", 100),
    FRESH("Fresh", 40),
    SEASONAL("Seasonal", 40),
    COFFEE_TEA_CHOCOLATE_DRINKS("Coffee & Tea & Chocolate Drinks", 40),
    SPORTS_DRINKS_ELECTROLYTES("Sports Drinks & Electrolytes", 40),
    JUICES_NECTARS_PUNCH("Juices & Nectars & Punch", 40),
    CIGARS_CIGARILLOS("Cigars & Cigarillos", 100),
    CIGARETTES("Cigarettes", 100),
    CLEANING("Cleaning", 40),
    HEALTH_AND_MEDICINE("Health & Medicine", 100);

    private final String key;
    private final int value;

    CategoryType(String key, int value) {
        this.key = key;
        this.value = value;
    }

    public static int fromKey(String key) {
        for (CategoryType categoryType : CategoryType.values()) {
            if (categoryType.getKey().equalsIgnoreCase(key)) {
                return categoryType.value;
            }
        }
        return 40;
    }

    public static CategoryType from(String key) {
        for (CategoryType categoryType : CategoryType.values()) {
            if (categoryType.getKey().equalsIgnoreCase(key)) {
                return categoryType;
            }
        }
        return MFC_CANDY_OTHER;
    }

    @Override
    public String toString() {
        return key + " -> " + value;
    }

}
