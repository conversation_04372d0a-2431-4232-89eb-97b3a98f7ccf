package com.mercaso.wms.application.dto;

import com.mercaso.wms.domain.warehouse.enums.WarehouseStatus;
import com.mercaso.wms.domain.warehouse.enums.WarehouseType;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WarehouseDto extends BaseDto {

    private UUID id;

    private String name;

    private WarehouseStatus status;

    private WarehouseType type;

}
