package com.mercaso.wms.interfaces.query;

import com.mercaso.wms.application.dto.shippingorder.ShippingOrderDto;
import com.mercaso.wms.application.queryservice.ShippingOrderQueryService;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/query/shipping-orders")
@RequiredArgsConstructor
public class QueryShippingOrderResource {

    private final ShippingOrderQueryService shippingOrderQueryService;

    @PreAuthorize("hasAuthority('wms:read:shipping-orders')")
    @GetMapping("/{id}")
    public ShippingOrderDto findById(@PathVariable UUID id) {
        return shippingOrderQueryService.findById(id);
    }

}
