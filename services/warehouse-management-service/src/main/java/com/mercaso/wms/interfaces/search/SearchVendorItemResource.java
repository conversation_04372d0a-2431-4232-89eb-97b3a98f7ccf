package com.mercaso.wms.interfaces.search;

import com.mercaso.wms.application.dto.Result;
import com.mercaso.wms.application.dto.VendorItemDto;
import com.mercaso.wms.application.queryservice.VendorItemQueryService;
import com.mercaso.wms.infrastructure.utils.DateUtils;
import jakarta.validation.constraints.NotNull;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.InputStreamResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Order;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Validated
@RestController
@RequestMapping("/search/vendor-items")
@RequiredArgsConstructor
public class SearchVendorItemResource {

    private final VendorItemQueryService vendorItemQueryService;

    @PreAuthorize("hasAuthority('wms:read:vendor-items')")
    @GetMapping
    public Result<VendorItemDto> search(@RequestParam @NotNull UUID warehouseId,
        @RequestParam(required = false) List<String> skuNumbers,
        @RequestParam int page,
        @RequestParam int pageSize) {
        Page<VendorItemDto> vendorItems = vendorItemQueryService.findVendorItems(warehouseId,
            skuNumbers == null ? Collections.emptyList() : skuNumbers,
            PageRequest.of(page - 1,
            pageSize,
            Sort.by(Order.desc("updated_at"))));

        return new Result<>(vendorItems.getContent(), vendorItems.getTotalElements());
    }

    @PreAuthorize("hasAuthority('wms:read:vendor-items')")
    @GetMapping("/export")
    public ResponseEntity<InputStreamResource> export(@RequestParam(required = false) UUID warehouseId,
        @RequestParam(required = false) List<String> skuNumbers) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Access-Control-Expose-Headers", "Content-Disposition");
        headers.add("Content-Disposition", "attachment; filename=mercaso-lookup-data-".concat(DateUtils.laDateTime()) + ".xlsx");
        return ResponseEntity.ok()
            .headers(headers)
            .contentType(MediaType.APPLICATION_OCTET_STREAM)
            .body(new InputStreamResource(vendorItemQueryService.export(warehouseId, skuNumbers)));
    }

}
