package com.mercaso.wms.delivery.application.search;

import com.mercaso.wms.delivery.application.dto.view.SearchDeliveryOrderView;
import com.mercaso.wms.delivery.application.query.DeliveryOrderQuery;
import com.mercaso.wms.delivery.infrastructure.repository.deliveryorder.jpa.DeliveryOrderJdbcTemplate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

@Slf4j
@Validated
@Service
@RequiredArgsConstructor
public class DeliveryOrderSearchService {

    private final DeliveryOrderJdbcTemplate jdbcTemplate;

    public Page<SearchDeliveryOrderView> search(DeliveryOrderQuery deliveryOrderQuery, Pageable pageable) {
        return jdbcTemplate.search(deliveryOrderQuery, pageable);
    }

}
