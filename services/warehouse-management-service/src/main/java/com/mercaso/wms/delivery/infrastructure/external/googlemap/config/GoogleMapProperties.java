package com.mercaso.wms.delivery.infrastructure.external.googlemap.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@Getter
@Setter
@ConfigurationProperties(prefix = "google.maps")
public class GoogleMapProperties {
    
    private String apiKey;
    private long connectTimeout = 10000;
    private long readTimeout = 10000;
} 