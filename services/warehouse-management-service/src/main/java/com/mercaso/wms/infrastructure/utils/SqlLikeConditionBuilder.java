package com.mercaso.wms.infrastructure.utils;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;

public class SqlLikeConditionBuilder {

    /**
     * Build SQL fragment for fuzzy matching (with OR combinations) and inject parameters into paramSource.
     *
     * @param columnName Database column name, e.g. "ddo.order_number"
     * @param values List of search keywords, e.g. ["abc", "123"]
     * @param paramPrefix Parameter prefix, e.g. "orderNumber" or "taskNumber"
     * @param paramSource For binding parameters
     * @return SQL condition fragment, e.g. "AND (ddo.order_number ilike :orderNumber0 OR ddo.order_number ilike :orderNumber1)"
     */
    public static String buildIlikeOrCondition(
        String columnName,
        List<String> values,
        String paramPrefix,
        MapSqlParameterSource paramSource
    ) {
        return buildIlikeOrCondition(columnName, values, paramPrefix, paramSource, true);
    }

    /**
     * Build SQL fragment for fuzzy matching (with OR combinations) and inject parameters into paramSource.
     *
     * @param columnName Database column name, e.g. "ddo.order_number"
     * @param values List of search keywords, e.g. ["abc", "123"]
     * @param paramPrefix Parameter prefix, e.g. "orderNumber" or "taskNumber"
     * @param paramSource For binding parameters
     * @param includeAnd Whether to include AND keyword before the condition
     * @return SQL condition fragment
     */
    public static String buildIlikeOrCondition(
        String columnName,
        List<String> values,
        String paramPrefix,
        MapSqlParameterSource paramSource,
        boolean includeAnd
    ) {
        if (values == null || values.isEmpty()) {
            return "";
        }

        String conditions = IntStream.range(0, values.size())
            .mapToObj(i -> {
                String paramName = paramPrefix + i;
                paramSource.addValue(paramName, "%" + values.get(i) + "%");
                return columnName + " ilike :" + paramName;
            })
            .collect(Collectors.joining(" OR "));

        return (includeAnd ? "AND " : "") + "(" + conditions + ") ";
    }

}
