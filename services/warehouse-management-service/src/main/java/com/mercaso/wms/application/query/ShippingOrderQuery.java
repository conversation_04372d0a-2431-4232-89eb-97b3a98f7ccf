package com.mercaso.wms.application.query;

import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@SuperBuilder
public class ShippingOrderQuery {

    private String orderNumber;

    private String deliveryDate;

    private List<String> statuses;

    private SortType sort;

    @Getter
    public enum SortType {
        CREATED_AT_DESC,
        CREATED_AT_ASC
    }

}
