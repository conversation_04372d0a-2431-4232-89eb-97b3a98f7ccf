package com.mercaso.wms.batch.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MfcLookupDto implements Serializable {

    @ExcelProperty(index = 9)
    private String itemNumber;
    @ExcelProperty(index = 15)
    private String itemDescription;
    @ExcelProperty(index = 19)
    private String department;
    @ExcelProperty(index = 20)
    private String category;
    @ExcelProperty(index = 21)
    private String subCategory;
    @ExcelProperty(index = 22)
    private String clazz;

}
