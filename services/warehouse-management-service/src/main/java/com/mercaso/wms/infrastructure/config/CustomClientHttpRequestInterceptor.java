package com.mercaso.wms.infrastructure.config;

import com.mercaso.wms.infrastructure.external.ums.AuthorizeAdaptor;
import jakarta.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

@Slf4j
@RequiredArgsConstructor
@Component
public class CustomClientHttpRequestInterceptor implements ClientHttpRequestInterceptor {

    private final AuthorizeAdaptor authorizeAdaptor;

    @NotNull
    @Override
    public ClientHttpResponse intercept(@NotNull HttpRequest request, @NotNull byte[] bytes, ClientHttpRequestExecution execution)
        throws IOException {
        
        String token = extractTokenFromCurrentRequest(request);
        
        if (token == null) {
            addM2MTokenToRequest(request);
        }
        
        return execution.execute(request, bytes);
    }
    
    private String extractTokenFromCurrentRequest(HttpRequest request) {
        return Optional.ofNullable(RequestContextHolder.getRequestAttributes())
            .filter(ServletRequestAttributes.class::isInstance)
            .map(ServletRequestAttributes.class::cast)
            .map(ServletRequestAttributes::getRequest)
            .map(currentRequest -> {
                copyContentTypeIfNeeded(request, currentRequest);
                return extractAndAddAuthHeader(request, currentRequest);
            })
            .orElse(null);
    }
    
    private void copyContentTypeIfNeeded(HttpRequest request, HttpServletRequest currentRequest) {
        if (!request.getHeaders().containsKey(HttpHeaders.CONTENT_TYPE)) {
            Optional.ofNullable(currentRequest.getContentType())
                .ifPresent(contentType -> request.getHeaders().add(HttpHeaders.CONTENT_TYPE, contentType));
        }
    }
    
    private String extractAndAddAuthHeader(HttpRequest request, HttpServletRequest currentRequest) {
        String token = currentRequest.getHeader(HttpHeaders.AUTHORIZATION);
        if (StringUtils.isNotBlank(token)) {
            request.getHeaders().add(HttpHeaders.AUTHORIZATION, token);
        }
        return token;
    }
    
    private void addM2MTokenToRequest(HttpRequest request) {
        authorizeAdaptor.getM2mToken()
            .filter(StringUtils::isNotBlank)
            .ifPresent(token -> 
                request.getHeaders().add(HttpHeaders.AUTHORIZATION, "Bearer " + token)
            );
    }
}
