package com.mercaso.wms.application.queryservice;

import static com.alibaba.excel.EasyExcelFactory.writerSheet;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.mercaso.ims.client.api.QueryItemRestApiApi;
import com.mercaso.ims.client.dto.ItemCategoryDto;
import com.mercaso.wms.application.dto.VendorItemDto;
import com.mercaso.wms.application.mapper.vendoritem.VendorItemDtoApplicationMapper;
import com.mercaso.wms.batch.enums.UploadDocNameEnum;
import com.mercaso.wms.batch.excel.converter.ListToStringConverter;
import com.mercaso.wms.domain.vendoritem.VendorItem;
import com.mercaso.wms.domain.vendoritem.VendorItemRepository;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import jakarta.validation.constraints.NotNull;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

@Slf4j
@Validated
@Service
@RequiredArgsConstructor
public class VendorItemQueryService {

    private final VendorItemRepository vendorItemRepository;
    private final QueryItemRestApiApi queryItemRestApi;

    private static final String FILE_PATH = "Mercaso lookup data template.xlsx";

    private static final Map<String, UploadDocNameEnum> WAREHOUSE_SHEET_NAME_MAP = Map.of(
        "MISSION", UploadDocNameEnum.MISSION,
        "MERCASO PRODUCE", UploadDocNameEnum.MERCASO_PRODUCE,
        "DOWNEY", UploadDocNameEnum.DOWNEY,
        "COSTCO", UploadDocNameEnum.COSTCO);

    public VendorItemDto findById(@NotNull UUID id) {
        return VendorItemDtoApplicationMapper.INSTANCE.domainToDto(vendorItemRepository.findById(id));
    }

    public Page<VendorItemDto> findVendorItems(UUID warehouseId, List<String> skuNumbers, Pageable pageable) {
        Page<VendorItem> vendorItems = vendorItemRepository.findByWarehouseIdAndSkuNumbers(warehouseId, skuNumbers, pageable);
        Page<VendorItemDto> map = vendorItems.map(VendorItemDtoApplicationMapper.INSTANCE::domainToDto);
        populateItemInfo(map.getContent());
        return map;
    }

    public ByteArrayInputStream export(UUID warehouseId, List<String> skuNumbers) {
        List<VendorItemDto> vendorItemDtos;
        if (warehouseId != null) {
            vendorItemDtos = findVendorItems(warehouseId,
                skuNumbers == null ? Collections.emptyList() : skuNumbers,
                Pageable.unpaged()).getContent();
        } else {
            vendorItemDtos = VendorItemDtoApplicationMapper.INSTANCE.domainToDtos(vendorItemRepository.findAll());
        }

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        Resource resource = new ClassPathResource("template/".concat(FILE_PATH));
        try (ExcelWriter excelWriter = EasyExcelFactory.write(outputStream)
            .withTemplate(resource.getInputStream())
            .registerConverter(new ListToStringConverter())
            .inMemory(true)
            .build()) {
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.FALSE).build();
            Map<String, List<VendorItemDto>> warehouseVendorItemsMap = vendorItemDtos.stream()
                .collect(Collectors.groupingBy(VendorItemDto::getWarehouseName));
            warehouseVendorItemsMap.forEach((warehouseName, vendorItemDtoList) -> {
                UploadDocNameEnum uploadDocNameEnum = WAREHOUSE_SHEET_NAME_MAP.get(warehouseName);
                if (uploadDocNameEnum != null) {
                    populateItemInfo(vendorItemDtoList);
                    excelWriter.fill(vendorItemDtoList, fillConfig, writerSheet(uploadDocNameEnum.getValue()).build());
                } else {
                    log.warn("Warehouse {} is not supported.", warehouseName);
                }
            });
            WAREHOUSE_SHEET_NAME_MAP.forEach((warehouseName, uploadDocNameEnum) -> {
                if (!warehouseVendorItemsMap.containsKey(warehouseName)) {
                    excelWriter.fill(Collections.emptyList(), fillConfig, writerSheet(uploadDocNameEnum.getValue()).build());
                }
            });

            excelWriter.finish();
            log.info("[export] Exported {} vendor items.", vendorItemDtos.size());
        } catch (IOException e) {
            throw new WmsBusinessException("Failed to export vendor items.", e);
        }
        return new ByteArrayInputStream(outputStream.toByteArray());
    }

    private void populateItemInfo(List<VendorItemDto> vendorItemsDtos) {
        ListUtils.partition(vendorItemsDtos, 50).forEach(vendorItems -> {
            Map<UUID, VendorItemDto> vendorItemDtoMap = vendorItems.stream()
                .collect(Collectors.toMap(VendorItemDto::getItemId, Function.identity()));
            List<ItemCategoryDto> itemsBySkuIn = queryItemRestApi.findItemsByIdIn(vendorItemDtoMap.keySet().stream().toList());
            if (itemsBySkuIn != null) {
                itemsBySkuIn.forEach(itemCategoryDto -> {
                    VendorItemDto vendorItemDto = vendorItemDtoMap.get(itemCategoryDto.getId());
                    if (vendorItemDto == null) {
                        return;
                    }
                    vendorItemDto.of(itemCategoryDto);
                });
            }
        });
    }
}
