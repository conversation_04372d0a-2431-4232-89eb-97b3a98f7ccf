package com.mercaso.wms.delivery.infrastructure.external.onesignal.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@Getter
@Setter
@ConfigurationProperties(prefix = "onesignal")
public class OnesignalProperties {

    private String appId;
    private String apiKey;
    private String createUserUrl;
    private String sendEmailUrl;
    private String invoiceTemplateId;
    private String getUserUrl;

}
