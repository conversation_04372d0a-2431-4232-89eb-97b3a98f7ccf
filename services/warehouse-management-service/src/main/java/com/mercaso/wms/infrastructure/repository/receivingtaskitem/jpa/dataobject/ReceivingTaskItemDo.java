package com.mercaso.wms.infrastructure.repository.receivingtaskitem.jpa.dataobject;

import com.mercaso.wms.infrastructure.repository.BaseDo;
import com.mercaso.wms.infrastructure.repository.receivingtask.jpa.dataobject.ReceivingTaskDo;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import java.util.UUID;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

@Entity
@Data
@EqualsAndHashCode(callSuper = false)
@ToString
@Table(name = "receiving_task_items")
@SQLDelete(sql = "update receiving_task_items set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
public class ReceivingTaskItemDo extends BaseDo {

    @ManyToOne
    @JoinColumn(name = "receiving_task_id", nullable = false)
    private ReceivingTaskDo receivingTask;

    @Column(name = "batch_item_id", nullable = false)
    private UUID batchItemId;

    @Column(name = "order_number")
    private String orderNumber;

    @Column(name = "item_id")
    private UUID itemId;

    @Column(name = "department")
    private String department;

    @Column(name = "category")
    private String category;

    @Column(name = "sku_number", length = 20)
    private String skuNumber;

    @Column(name = "title")
    private String title;

    @Column(name = "location_id")
    private UUID locationId;

    @Column(name = "location_name", length = 50)
    private String locationName;

    @Column(name = "aisle_number", length = 50)
    private String aisleNumber;

    @Column(name = "receiving_sequence")
    private Integer receivingSequence;

    @Column(name = "expect_qty")
    private Integer expectQty;

    @Column(name = "received_qty")
    private Integer receivedQty;

    @Column(name = "error_info")
    private String errorInfo;

    @Column(name = "breakdown_name", length = 20)
    private String breakdownName;

    @Column(name = "line")
    private Integer line;

    @Column(name = "shipping_order_id")
    private UUID shippingOrderId;

    @Column(name = "shipping_order_item_id")
    private UUID shippingOrderItemId;

}