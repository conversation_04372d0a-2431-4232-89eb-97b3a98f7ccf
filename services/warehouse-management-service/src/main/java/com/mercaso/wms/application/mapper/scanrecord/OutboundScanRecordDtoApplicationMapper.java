package com.mercaso.wms.application.mapper.scanrecord;

import com.mercaso.wms.application.dto.scanrecord.OutboundScanRecordDto;
import com.mercaso.wms.application.mapper.BaseDtoApplicationMapper;
import com.mercaso.wms.domain.scanrecords.OutboundScanRecord;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OutboundScanRecordDtoApplicationMapper extends
    BaseDtoApplicationMapper<OutboundScanRecord, OutboundScanRecordDto> {

    OutboundScanRecordDtoApplicationMapper INSTANCE = Mappers.getMapper(OutboundScanRecordDtoApplicationMapper.class);

}
