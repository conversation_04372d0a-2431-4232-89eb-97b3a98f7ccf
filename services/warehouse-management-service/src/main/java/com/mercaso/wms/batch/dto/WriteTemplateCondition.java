package com.mercaso.wms.batch.dto;

import com.mercaso.wms.application.dto.BatchDto;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WriteTemplateCondition {

    private List<ExcelBatchDto> excelBatchDtos;

    private List<BreakdownDto> bigBreakdownDtos;

    private List<BreakdownDto> smallBreakdownDtos;

    private String taggedWith;

    private List<String> fileNames;

    private Map<String, List<ExcelBatchDto>> sourceAndListMap;

    private List<ExcelBatchDto> mfcBeverageBigOrder;

    private List<ExcelBatchDto> mfcBeverageSmallOrder;

    private List<ExcelBatchDto> mfcCandyOrderItems;

    private List<BatchDto> existingBatchDtos;

}
