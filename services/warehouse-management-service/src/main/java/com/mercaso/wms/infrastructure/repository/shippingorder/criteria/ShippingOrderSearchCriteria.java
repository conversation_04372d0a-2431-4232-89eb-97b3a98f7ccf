package com.mercaso.wms.infrastructure.repository.shippingorder.criteria;


import com.mercaso.wms.application.query.ShippingOrderQuery;
import com.mercaso.wms.domain.shippingorder.enums.ShippingOrderStatus;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ShippingOrderSearchCriteria {

    private String orderNumber;

    private String deliveryDate;

    private List<ShippingOrderStatus> statuses;

    private ShippingOrderQuery.SortType sort;


}