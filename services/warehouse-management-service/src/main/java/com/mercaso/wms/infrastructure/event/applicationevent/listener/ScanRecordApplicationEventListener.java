package com.mercaso.wms.infrastructure.event.applicationevent.listener;

import com.mercaso.wms.application.dto.event.OutboundScanRecordCreatedApplicationEvent;
import com.mercaso.wms.application.service.ShippingOrderApplicationService;
import com.mercaso.wms.infrastructure.retryabletransaction.RetryableTransaction;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.event.TransactionalEventListener;


@Slf4j
@Component
@RequiredArgsConstructor
public class ScanRecordApplicationEventListener {

    private final ShippingOrderApplicationService shippingOrderApplicationService;

    @RetryableTransaction(propagation = Propagation.REQUIRES_NEW)
    @TransactionalEventListener
    public void handleEvent(OutboundScanRecordCreatedApplicationEvent applicationEvent) {
        log.info("Handling OutboundScanRecordCreatedApplicationEvent: {}", applicationEvent);
        shippingOrderApplicationService.update(applicationEvent.getPayload().getData());
    }
}
