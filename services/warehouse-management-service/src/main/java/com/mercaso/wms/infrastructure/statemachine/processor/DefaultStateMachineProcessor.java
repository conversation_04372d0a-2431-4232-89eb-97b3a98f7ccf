package com.mercaso.wms.infrastructure.statemachine.processor;

import com.mercaso.wms.infrastructure.statemachine.StateTransitionType;
import com.mercaso.wms.infrastructure.statemachine.StateType;
import com.mercaso.wms.infrastructure.statemachine.StatefulContext;
import com.mercaso.wms.infrastructure.statemachine.factory.StateEventHandler;
import com.mercaso.wms.infrastructure.statemachine.factory.WmsStateMachineFactory;
import org.springframework.stereotype.Component;

@Component
public class DefaultStateMachineProcessor<T extends StatefulContext<S>, S extends StateType, E extends StateTransitionType> implements
    StateMachineProcessor<T, S, E> {

    @Override
    public void processEvent(T domain, E event) {
        WmsStateMachineFactory<S, E, T> wmsStateMachineFactory = this.getStateMachineFactory(domain);

        StateEventHandler.processEvent(wmsStateMachineFactory, domain, event);
    }
}
