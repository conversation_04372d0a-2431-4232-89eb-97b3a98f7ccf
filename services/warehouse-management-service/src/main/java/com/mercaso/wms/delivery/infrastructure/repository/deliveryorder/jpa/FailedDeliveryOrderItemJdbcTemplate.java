package com.mercaso.wms.delivery.infrastructure.repository.deliveryorder.jpa;

import com.mercaso.wms.delivery.application.dto.view.SearchFailedDeliveryOrderItemView;
import com.mercaso.wms.delivery.application.query.FailedDeliveryOrderItemQuery;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class FailedDeliveryOrderItemJdbcTemplate {

    private final NamedParameterJdbcTemplate jdbcTemplate;

    public Page<SearchFailedDeliveryOrderItemView> search(FailedDeliveryOrderItemQuery query, Pageable pageable) {
        MapSqlParameterSource params = new MapSqlParameterSource();

        FailedDeliveryOrderItemSearchSqlBuilder sqlBuilder = FailedDeliveryOrderItemSearchSqlBuilder.builder()
            .query(query)
            .pageable(pageable)
            .params(params)
            .build();

        String selectSql = sqlBuilder.buildSelectSql();
        String countSql = sqlBuilder.buildCountSql();

        Long total = jdbcTemplate.queryForObject(countSql, params, Long.class);

        List<SearchFailedDeliveryOrderItemView> content = jdbcTemplate.query(selectSql, params,
            (rs, rowNum) -> convert(rs));

        return new PageImpl<>(content, pageable, total != null ? total : 0);
    }

    private SearchFailedDeliveryOrderItemView convert(ResultSet rs) throws SQLException {
        SearchFailedDeliveryOrderItemView view = new SearchFailedDeliveryOrderItemView();
        view.setId(rs.getObject("id", UUID.class));
        view.setDeliveryOrderId(rs.getObject("deliveryOrderId", UUID.class));
        view.setDeliveryDate(rs.getString("delivery_date"));
        view.setOrderNumber(rs.getString("order_number"));
        view.setDriverUserId(rs.getObject("driver_user_id", UUID.class));
        view.setDriverUserName(rs.getString("driver_user_name"));
        view.setReasonCode(rs.getString("reason_code"));
        view.setLine(rs.getInt("line"));
        view.setSkuNumber(rs.getString("sku_number"));
        view.setTitle(rs.getString("title"));
        view.setReasonQty(rs.getBigDecimal("reasonQty"));
        view.setUpdatedAt(rs.getString("updated_at"));
        return view;
    }
}
