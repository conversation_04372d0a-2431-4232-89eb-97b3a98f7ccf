package com.mercaso.wms.application.dto;

import com.fasterxml.jackson.databind.JsonNode;
import com.mercaso.wms.domain.batch.enums.BatchStatus;
import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BatchDto extends BaseDto {

    private UUID id;

    private JsonNode original;

    private JsonNode generated;

    private String tag;

    private String lastModifiedBy;

    private String number;

    private BatchStatus status;

    private Instant createdAt;

    private String createdBy;

    private Instant updatedAt;

    private String finaleTransferShipmentNumber;

    private JsonNode finaleEntities;

}
