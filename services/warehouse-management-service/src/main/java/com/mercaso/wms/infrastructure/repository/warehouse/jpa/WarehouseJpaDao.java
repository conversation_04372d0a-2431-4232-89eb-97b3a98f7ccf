package com.mercaso.wms.infrastructure.repository.warehouse.jpa;

import com.mercaso.wms.domain.warehouse.enums.WarehouseType;
import com.mercaso.wms.infrastructure.repository.warehouse.jpa.dataobject.WarehouseDo;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface WarehouseJpaDao extends JpaRepository<WarehouseDo, UUID> {

    @Query("SELECT w FROM WarehouseDo w WHERE w.status = 'ACTIVE' and w.type = :type")
    List<WarehouseDo> findByType(WarehouseType type);

    WarehouseDo findByName(String name);
}
