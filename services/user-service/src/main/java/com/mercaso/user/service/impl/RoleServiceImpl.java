package com.mercaso.user.service.impl;

import com.mercaso.user.adaptor.Auth0ManagementApiAdaptor;
import com.mercaso.user.dto.user.RoleDto;
import com.mercaso.user.service.RoleService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class RoleServiceImpl implements RoleService {

    private final Auth0ManagementApiAdaptor auth0ManagementApiAdaptor;

    @Override
    public List<RoleDto> listRoles() {
        log.info("Listing all user group roles");
        return auth0ManagementApiAdaptor.listRoles();
    }
} 