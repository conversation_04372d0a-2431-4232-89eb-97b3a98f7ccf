package com.mercaso.user.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletRequestWrapper;
import java.io.IOException;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;

@Configuration
public class SwaggerConfig {

    // API Configuration Constants
    private static final String BASE_PACKAGE = "com.mercaso.user.controller";
    private static final String API_TITLE = "User Service API";
    private static final List<String> API_VERSIONS = List.of("v1");
    private static final String X_FORWARDED_PREFIX = "x-forwarded-prefix";

    // Contact Information Constants
    private static final String TEAM_NAME = "Mercaso Team";

    @Bean
    public OpenAPI openAPI() {
        return new OpenAPI()
            .info(new Info()
                .title(API_TITLE)
                .description(buildApiDescription())
                .contact(createContactInfo())
            );
    }

    private Contact createContactInfo() {
        return new Contact()
            .name(TEAM_NAME);
    }

    @Bean
    public List<GroupedOpenApi> apiGroups() {
        return API_VERSIONS.stream()
            .map(this::createApiGroup)
            .filter(Objects::nonNull)
            .toList();
    }

    private GroupedOpenApi createApiGroup(String version) {
        String packagePath = BASE_PACKAGE + "." + version;
        return GroupedOpenApi.builder()
            .group(version)
            .packagesToScan(packagePath)
            .build();
    }

    private String buildApiDescription() {
        return String.format("API Documentation for %s", API_TITLE);
    }

    @Bean
    public FilterRegistrationBean<Filter> swaggerHttpRequestWrapperFilter(@Value("${spring.application.name}") String appName) {
        boolean runningInk8s = System.getenv().keySet().stream().anyMatch(x -> x.startsWith("KUBERNETES_"));

        if (runningInk8s) {
            FilterRegistrationBean<Filter> filter = new FilterRegistrationBean<>();
            filter.setFilter(new SwaggerHttpRequestWrapperFilter(appName));
            filter.setOrder(Ordered.HIGHEST_PRECEDENCE);
            filter.addUrlPatterns(
                "/swagger-ui/*",
                "/swagger-ui.html",
                "/v3/api-docs/*",
                "/v3/api-docs"
            );
            return filter;
        } else {
            FilterRegistrationBean<Filter> filter = new FilterRegistrationBean<>();
            filter.addUrlPatterns(
                "/swagger-ui/*",
                "/swagger-ui.html",
                "/v3/api-docs/*",
                "/v3/api-docs"
            );
            filter.setFilter((request, response, chain) -> chain.doFilter(request, response));
            return filter;
        }
    }

    // In order for the swagger-ui to work behind our reverse proxy (currently nginx) it needs
    // to be supplied the `x-forwarded-prefix` header.  While this can be done in k8s with the nginx proxy,
    // the header's value cannot vary by each `path` specified in the ingress and instead only set for the entire
    // ingress config.  Since we utilize multiple paths per ingress we have to use the hack below to make an
    // `x-forwarded-prefix` header appear out of thin air.
    static class SwaggerHttpRequestWrapperFilter implements Filter {

        private final String prefix;

        SwaggerHttpRequestWrapperFilter(String prefix) {
            // All of our ingress definitions remove the `-service` from the path.  Remove the suffix if the application
            // name includes it.
            if (prefix.endsWith("-service")) {
                prefix = prefix.replace("-service", "");
            }
            this.prefix = "/" + prefix;
        }

        @Override
        public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
            HttpServletRequest req = (HttpServletRequest) request;
            var wrapper = new HttpServletRequestWrapper(req) {
                @Override
                public String getHeader(String name) {
                    if (name.equalsIgnoreCase(X_FORWARDED_PREFIX)) {
                        return prefix;
                    }
                    return super.getHeader(name);
                }

                @Override
                public Enumeration<String> getHeaders(String name) {
                    if (name.equalsIgnoreCase(X_FORWARDED_PREFIX)) {
                        return Collections.enumeration(List.of(prefix));
                    }
                    return super.getHeaders(name);
                }

                @Override
                public Enumeration<String> getHeaderNames() {
                    Set<String> names = new HashSet<>();
                    Enumeration<String> iter = super.getHeaderNames();
                    while (iter.hasMoreElements()) {
                        names.add(iter.nextElement());
                    }
                    names.add(X_FORWARDED_PREFIX);
                    return Collections.enumeration(names);
                }
            };

            chain.doFilter(wrapper, response);
        }

    }
}