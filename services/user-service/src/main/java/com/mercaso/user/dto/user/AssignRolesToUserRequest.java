package com.mercaso.user.dto.user;

import jakarta.validation.constraints.NotEmpty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class AssignRolesToUserRequest {

    /**
     * List of role_id to assign to the user
     */
    @NotEmpty(message = "roles is required")
    private List<String> roles;
} 