spring:
  cloud:
    vault:
      enabled: false

security:
  enable-method-security: false
  public-paths: /**

springdoc:
  swagger-ui:
    enabled: true

auth0:
  mgmt:
    domain: mock-auth0-domain
    client-id: mock-DH78e4ykzpwXMeNp11lYout7nkcBOMCg
    client-secret: mock-OIdcEKy99vtT9HjoYzjXTeREV60rth-37DGTrjxAQjDQvqZT4Am_vcJLOO0z2TV3
  role-mapping:
    user_group_to_role:
      mock-rol_5tSuF0YGZBy2xstI: rol_Db7YjfzWv5j1cfBp
      mock-rol_l4ySkzxBbNBpJffT: rol_P955BxP3YFjoCyxD, rol_Db7YjfzWv5j1cfBp
  m2m:
    clients:
      mock-JJE0LIDKSphApe9LSc54ciG0z2LO13IH: airflow-key-d86bd545-2e1c-4766-85cc-9d290273bc68
